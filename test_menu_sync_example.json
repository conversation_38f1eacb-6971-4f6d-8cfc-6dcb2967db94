{"test_cases": [{"name": "Sync All Items (Backward Compatible)", "method": "POST", "url": "/v1/brand-service/sites/{site_id}/menu/sync", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}"}, "body": {}, "expected_response": {"success": true, "sync_request_id": "string", "message": "Menu synchronization started"}}, {"name": "Sync Specific Items", "method": "POST", "url": "/v1/brand-service/sites/{site_id}/menu/sync", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}"}, "body": {"sync_items": ["item_123", "item_456", "item_789"], "delete_items": []}, "expected_response": {"success": true, "sync_request_id": "string", "message": "Menu synchronization started"}}, {"name": "Sync with Delete Items (Future)", "method": "POST", "url": "/v1/brand-service/sites/{site_id}/menu/sync", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {token}"}, "body": {"sync_items": ["item_123", "item_456"], "delete_items": ["item_789", "item_999"]}, "expected_response": {"success": true, "sync_request_id": "string", "message": "Menu synchronization started"}}, {"name": "Get Sync Status", "method": "GET", "url": "/v1/brand-service/sites/{site_id}/menu/sync?status=processing&page=1&limit=10", "headers": {"Authorization": "Bearer {token}"}, "expected_response": {"success": true, "data": [{"id": "string", "site_id": "string", "menu_data": {}, "callback": [], "status": "processing", "started_at": "2023-12-01T10:00:00Z", "completed_at": null, "sync_items": ["item_123", "item_456"], "delete_items": [], "created_at": "2023-12-01T10:00:00Z", "updated_at": "2023-12-01T10:00:00Z"}], "total": 1, "page": 1, "page_size": 10, "total_pages": 1, "has_next": false, "has_prev": false}}], "menu_sync_log_examples": [{"description": "Successful Grab sync log", "data": {"id": "log_123", "sync_request_id": "req_456", "site_id": "site_789", "platform": "grab", "status": "success", "error_msg": "", "request_data": {"sync_items": ["item_123", "item_456"], "delete_items": []}, "response_data": {"synced_items": ["item_123", "item_456"]}, "sync_items": ["item_123", "item_456"], "delete_items": [], "completed_time": "2023-12-01T10:05:00Z", "created_at": "2023-12-01T10:00:00Z", "updated_at": "2023-12-01T10:05:00Z"}}, {"description": "Failed BE sync log", "data": {"id": "log_124", "sync_request_id": "req_456", "site_id": "site_789", "platform": "be", "status": "failed", "error_msg": "item item_999 not found in BE menu", "request_data": {"sync_items": ["item_999"], "delete_items": []}, "response_data": {"errors": ["item item_999 not found in BE menu"], "synced_items": []}, "sync_items": ["item_999"], "delete_items": [], "completed_time": "2023-12-01T10:05:30Z", "created_at": "2023-12-01T10:00:00Z", "updated_at": "2023-12-01T10:05:30Z"}}]}