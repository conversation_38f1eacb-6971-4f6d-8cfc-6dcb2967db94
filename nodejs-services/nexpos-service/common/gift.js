const { RetailerSaleConfig, Order, Provinces } = require('../../.shared/database')
const _ = require('lodash')
const { get_location } = require('../../.shared/delivery/gmap')
const helper = require('../../.shared/helper')

const _calculate_new_price_by_xy = (selected_gift, discount_type, discount_value) => {
  switch (discount_type) {
    case 'fixed':
      return selected_gift.price - discount_value
    case 'percent':
      return selected_gift.price * (1 - discount_value / 100)
    // flat price
    default:
      return discount_value
  }
}

const get_cart_address_province = async (address) => {
  const location = await get_location(address)

  if (!location) {
    return null
  }
  const admin_area_level_1 = location.address_components.find((v) => v.types.includes('administrative_area_level_1'))

  if (!admin_area_level_1) {
    return null
  }

  const long_name = admin_area_level_1.long_name
  const slug = helper.text_slugify(long_name)

  const provinces = await Provinces.find({}).lean()
  const province = provinces.find((v) => v.google_names.includes(long_name) || slug.includes(v.code))

  if (!province) {
    return null
  }

  return province.code
}

const _get_total_option_price = (options = []) => {
  if (!Array.isArray(options)) {
    return 0
  }
  const raw_total_option_price = _.sum(options?.map?.((childOptions) => _.sumBy(childOptions, (option) => (option?.quantity || 1) * (option?.price || 0))))
  return isNaN(raw_total_option_price) ? 0 : raw_total_option_price
}

const get_sale_configs = async (site, nexpos_vouchers) => {
  const configs = await RetailerSaleConfig.find({
    brand_id: site.brand_id,
    active: true,
  })
    .sort({ order: 1 })
    .lean()

  const brand_sale_configs = configs.filter((c) => {
    if (c.start_date && new Date(c.start_date) > new Date()) {
      return false
    }
    if (c.end_date && new Date(c.end_date) < new Date()) {
      return false
    }
    if (nexpos_vouchers && nexpos_vouchers.length > 0) {
      return c.voucher_config?.voucher_code && nexpos_vouchers.includes(c.voucher_config?.voucher_code)
    }
    return !c.voucher_config?.voucher_code
  })
  return brand_sale_configs
}

const _apply_gifts = async ({ 
  user,
  site,
  menu_items,
  cart,
  selected_gifts,
  option_categories,
  dpoint_voucher,
  brand_sale_configs,
}) => {
  let result = { gifts: [], discount: [], ship_discount: [] }
  if (!site.apply_gift || cart.dishes.length === 0) {
    return result
  }

  if (brand_sale_configs.length > 0) {
    const is_first_order = user
      ? await Order.findOne({ user_id: user._id, status: { $in: ['PENDING', 'DOING', 'PICK', 'FINISH'] } })
          .lean()
          .then((res) => !res)
      : false

    for (const sale_config of brand_sale_configs) {
      console.log('sale_config', sale_config.name, sale_config._id, sale_config.type)
      const { config, type, name, start_date, end_date } = sale_config
      const is_applicable = !!user && (config?.first_order_only ? is_first_order : true)

      // check for start_date and end_date, if not in range, skip, if not set, always apply
      if (start_date && new Date(start_date) > new Date()) {
        continue
      }
      if (end_date && new Date(end_date) < new Date()) {
        continue
      }

      if (type === 'order_bonus') {
        /*
          schema:
          {
            "type": "order_bonus",
            "items": [
              {
                "min_order_amount": 1000000,
                "gift_item_name": "Cơm gà",
                "gift_category_name": "Món chính",
                "gift_quantity": 1,
                "gift_price": 0 // price of gift item can be 0 or more
                "force_get_y": false // if true, will always add gift item to cart
              }
            ]
          }
        */
        const sub_total = _.sumBy(cart?.dishes, 'price') || 0
        const applied_config = config.items?.sort((a, b) => b.min_order_amount - a.min_order_amount).find((v) => sub_total >= v.min_order_amount)

        if (applied_config) {
          const gift_item = _.find(menu_items, (v) => v.name === applied_config.gift_item_name && v.category_name === applied_config.gift_category_name)

          if (gift_item) {
            const selected_gift = selected_gifts?.find((v) => v.config_id === String(sale_config._id))
            const applied_gift =
              selected_gift ||
              cart.gifts?.find((v) => v.name === applied_config.gift_item_name && v.category_name === applied_config.gift_category_name && String(v.config_id) === String(sale_config._id))

            if (applied_gift) {
              const total_option_price = _get_total_option_price(applied_gift.options)

              applied_config.gift_quantity = applied_config.gift_quantity || 1
              result.gifts.push({
                ...applied_gift,
                ...gift_item,
                quantity: applied_config.gift_quantity,
                // unit_price: 0,
                name: applied_config.gift_item_name,
                category_name: applied_config.gift_category_name,
                note: name,
                is_gift: true,
                config_id: sale_config._id,
                is_applicable,
                price: applied_config.gift_quantity * (Number(applied_config.gift_price || 0) + Number(total_option_price)),
              })
            }
          }
        }
      }

      if (type === 'buy_x_get_y') {
        /*
          schema:
          {
            "type": "buy_x_get_y",
            "item_name": "Cơm gà",
            "category_name": "Món chính",
            "quantity": 2,
            "gift_item_name": "Cơm gà",
            "gift_category_name": "Món chính",
            "gift_quantity": 1,
            "gift_price": 0 // price of gift item can be 0 or more
            force_get_y: false // if true, will always add gift item to cart
          }
        */
        let cart_item_count = 0
        cart.dishes.forEach((d) => {
          if (d.name === config.item_name && d.category_name === config.category_name) {
            cart_item_count += d.quantity
          }
        })

        const no_of_gifts = Math.floor(cart_item_count / config.quantity) * config.gift_quantity
        const gift_item = _.find(menu_items, (v) => v.name === config.gift_item_name && v.category_name === config.gift_category_name)
        if (no_of_gifts > 0 && gift_item) {
          const selected_gift = selected_gifts?.find((v) => v.config_id === String(sale_config._id))
          const applied_gift =
            selected_gift || cart.gifts?.find((v) => v.name === config.gift_item_name && v.category_name === config.gift_category_name && String(v.config_id) === String(sale_config._id))

          if (applied_gift) {
            const total_option_price = _get_total_option_price(applied_gift.options)
            result.gifts.push({
              ...applied_gift,
              ...gift_item,
              name: config.gift_item_name,
              category_name: config.gift_category_name,
              quantity: no_of_gifts,
              // unit_price: 0,
              note: name,
              is_gift: true,
              config_id: sale_config._id,
              is_applicable,
              price: no_of_gifts * (Number(config.gift_price || 0) + Number(total_option_price)),
            })
          }
        }
      }

      if (type === 'buy_x_discount_y') {
        /*
          new schema:
          {
            "item_type": "category" | "dish" | "option",
            "items": [{name, category_name}], 
            "quantity": 2,
            "gift_items": [{name, category_name}],
            "gift_quantity": 1,
            "discount_type": "fixed" | "percent" | 'flat', 
            "discount_value": 0 // fixed value or percent
            force_get_y: false // if true, will always add gift item to cart
          }          
        */

        const { item_type, items, quantity, gift_items, gift_quantity, discount_type, discount_value, force_get_y } = config

        if (item_type === 'category' || item_type === 'dish') {
          let config_item_in_menu = []
          if (item_type === 'dish') {
            config_item_in_menu = menu_items.filter((v) => items.some((item) => item.name === v.name && item.category_name === v.category_name))
          } else {
            config_item_in_menu = menu_items.filter((v) => items.some((item) => item.category_name === v.category_name))
          }

          const applicable_items = cart.dishes.filter((d) => config_item_in_menu.some((item) => item.name === d.name && item.category_name === d.category_name))
          const total_applicable_items = _.sumBy(applicable_items, 'quantity')
          if (total_applicable_items >= quantity) {
            const max_no_of_gifts = Math.floor(total_applicable_items / quantity) * gift_quantity

            // find the gift from the cart, with lower price
            const applicable_gifts = cart.dishes.filter((d) => gift_items.some((item) => item.name === d.name && item.category_name === d.category_name)).sort((a, b) => a.unit_price - b.unit_price)
            let no_of_gifts = max_no_of_gifts
            applicable_gifts.forEach((item) => {
              if (no_of_gifts > 0) {
                const item_to_get_discount = Math.min(item.quantity, no_of_gifts)
                const price_after_discount = _calculate_new_price_by_xy({ ...item, price: item.unit_price }, discount_type, discount_value)
                const total_discount = item_to_get_discount * (item.unit_price - price_after_discount)

                result.discount.push({
                  dish_id: item._id,
                  code: item.code,
                  category_name: item.category_name,
                  item_name: item.name,
                  amount: Math.floor(total_discount),
                  note: name,
                  config_id: sale_config._id,
                  is_applicable: true,
                  type: 'item_discount',
                  item: item,
                })
              }
              no_of_gifts = no_of_gifts - item.quantity
            })
          }
        } else if (item_type === 'option') {
          for (const item of cart.dishes) {
            // options is nested array
            const applicable_options = item.options.flat().filter((option) => items.some((g) => g.name === option.option_item && g.category_name === option.option_name))

            // if applicable_options.length >= quantity, then create applied gift
            if (applicable_options.length >= quantity) {
              const applied_gift_options = item.options.flat().filter((o) => gift_items.some((g) => g.name === o.option_item && g.category_name === o.option_name))

              // sort by price, take the cheapest, but before that, populate with original price
              const sorted_by_price = applied_gift_options
                .map((option) => {
                  // console.log('option_categories', option_categories.find((c) => c.name === option.option_name), option.option_name)
                  const option_in_menu = option_categories.find((c) => c.name === option.option_name)?.options.find((i) => i.name === option.option_item)
                  console.log('option_in_menu', option_in_menu)
                  if (option_in_menu) {
                    return { ...option, price: option_in_menu.price }
                  }
                  return null
                })
                .filter((v) => !!v)
                .sort((a, b) => a.price - b.price)
                .slice(0, gift_quantity)

              const original_option_prices = _.sumBy(sorted_by_price, 'price')
              sorted_by_price.forEach((option) => {
                const new_price = _calculate_new_price_by_xy(option, discount_type, Number(discount_value))
                option.price = new_price
              })

              const total_discounted_price = _.sumBy(sorted_by_price, 'price')

              // item.options.forEach((options, index) => {
              //   item.options[index] = options.map((option) => {
              //     const found = sorted_by_price.find((v) => v.option_item === option.option_item && v.option_name === option.option_name)
              //     if (found) {
              //       return { ...option, price: found.price, note: name }
              //     }
              //     return option
              //   })
              // })

              if (original_option_prices - total_discounted_price) {
                result.discount.push({
                  dish_id: item._id,
                  code: item.code,
                  category_name: item.category_name,
                  item_name: item.name,
                  amount: Math.floor(original_option_prices - total_discounted_price),
                  note: name,
                  config_id: sale_config._id,
                  is_applicable: true,
                  type: 'option_discount',
                })
              }
            }
          }
        }
      }

      if (type === 'discount') {
        /*
          schema:
          {
            "type": "discount",
            "item_name": "Cơm gà",
            "category_name": "Món chính",
            "quantity": 2,
            "discount": 10000
          }
        */

        const cart_dishes_union = cart.dishes.reduce((acc, cur) => {
          const found = acc.find((v) => v.name === cur.name && v.category_name === cur.category_name)
          if (found) {
            found.quantity += cur.quantity
            found.price += cur.price
          } else {
            acc.push({ ...cur })
          }
          return acc
        }, [])

        const dish_meet_req = cart_dishes_union.some((d) => d.name === config.item_name && d.category_name === config.category_name && d.quantity >= config.quantity)

        if (dish_meet_req) {
          result.discount.push({
            // code: dish_meet_req.code,
            amount: config.discount,
            note: name,
            config_id: sale_config._id,
            is_applicable,
          })
        }
      }

      if (type === 'percent_discount') {
        /*
          schema:
          {
            "type": "percent_discount",
            "items": [
              {
                "item_name": "Cơm gà",
                "category_name": "Món chính",
                "percent_discount": 10
              }
            ]
          }
        */

        const config_items = config.items
        const dish_meet_req = cart.dishes.filter((d) => config_items.some((item) => (item.item_name === d.name && item.category_name === d.category_name) || 
        (item.item_name === d.name && item.category_name === d.sub_category_name)))
        
        dish_meet_req.forEach((dish) => {
          const discount = (config.percent_discount * dish.price || 0) / 100
          result.discount.push({
            dish_id: dish._id,
            code: dish.code,
            category_name: dish.category_name,
            item_name: dish.name,
            amount: Math.floor(discount),
            note: name,
            config_id: sale_config._id,
            is_applicable,
            type: 'item_discount',
          })
        })
      }

      if (type === 'order_discount') {
        /*
          schema:
          {
            "type": "order_discount",
            "items": [
              {
                "min_value": 1000000,
                "discount": 10000,
                "type": "fixed" | "percent",
                "max_discount"?: 100000
              }
            ]
          }
        */
        const sub_total = _.sumBy(cart?.dishes, 'price') ?? 0
        // total_after_discount is after every discount and coupon applied
        const total_after_discount =
          sub_total -
          (_.sumBy(
            result.discount.filter((d) => d.is_applicable),
            'amount'
          ) || 0) -
          (dpoint_voucher?.discount || 0)

        const final_discount = Math.max(
          ...config.items
            ?.filter((item) => item.min_value < total_after_discount)
            .map((item) => {
              if (item.type === 'fixed') {
                return item.discount
              }
              if (item.type === 'percent') {
                const discount = Math.floor((item.discount * total_after_discount) / 100)
                return item.max_discount ? Math.min(discount, item.max_discount) : discount
              }
              return 0
            }),
          0
        )

        final_discount &&
          result.discount.push({
            amount: final_discount,
            note: name,
            config_id: sale_config._id,
            is_applicable,
          })
      }

      if (type === 'fixed_discount') {
        /*
          schema:
          {
            "type": "fixed_discount",
            "items": [
              {
                "item_name": "Cơm gà",
                "category_name": "Món chính",
                "discount": 10000
              }
            ]
          }
        */
        const config_items = config.items
        const dish_meet_req = cart.dishes.filter((d) => config_items.some((item) => (item.item_name === d.name && item.category_name === d.category_name) || (
          item.item_name === d.name && item.category_name === d.sub_category_name
        )))
        // console.log('dish_meet_req', dish_meet_req)
        dish_meet_req.forEach((dish) => {
          const discount = Math.min(dish.price, config_items.find((item) => item.item_name === dish.name && item.category_name === dish.category_name).discount) || 0
          result.discount.push({
            dish_id: dish._id,
            code: dish.code,
            category_name: dish.category_name,
            item_name: dish.name,
            amount: Math.floor(discount * dish.quantity),
            note: name,
            config_id: sale_config._id,
            is_applicable,
            type: 'item_discount',
          })
        })
      }
    }

    // filter out the discount with same dish_id, only keep the highest discount
    result.discount = result.discount.reduce((acc, cur) => {
      const found = acc.find((v) => v.dish_id === cur.dish_id && v.type === 'item_discount' && cur.type === 'item_discount')
      if (found) {
        if (found.amount < cur.amount) {
          found.amount = cur.amount
        }
      } else {
        acc.push(cur)
      }
      return acc
    }, [])

    // handle ship_discount after all discount applied
    const sub_total = _.sumBy(cart?.dishes, 'price') || 0
    const total_after_discount =
      sub_total -
      (_.sumBy(
        result.discount.filter((d) => d.is_applicable),
        'amount'
      ) || 0) -
      (dpoint_voucher?.discount || 0)

    const shipping_fee = cart.shipment.service?.price || 0
    const ship_discount_config = brand_sale_configs.find((v) => v.type === 'ship_discount')
    if (ship_discount_config) {
      /*
        schema:
        {
          "type": "ship_discount",
          "items": [
            {
              "min_value": 1000000, // min order value to apply discount
              "type": "discount" | "free_ship" | "flat_rate"
              "value"?: 10000 // value of discount or flat rate
            }
          ],
          provinces: [1, 2, 3] // list of province_id to apply discount
        }
      */

        // console.log('ship_discount_config', ship_discount_config)
      if (ship_discount_config.config?.provinces?.length) {
        const customer_address = cart.shipment.to?.address
        if (!customer_address) {
          return result
        }

        const province = await get_cart_address_province(customer_address)
        const is_applicable_province = ship_discount_config.config.provinces.includes(province)

        if (!is_applicable_province) {
          return result
        }
      }

      const config_discount = Math.max(
        ...ship_discount_config?.config?.items
          .filter((item) => item.min_value <= total_after_discount)
          .map((item) => {
            if (item.type === 'discount') {
              return item.value
            }
            if (item.type === 'free_ship') {
              return shipping_fee
            }
            if (item.type === 'flat_rate') {
              return shipping_fee > item.value ? shipping_fee - item.value : 0
            }
            return 0
          }),
        0
      )

      const final_discount = Math.min(config_discount, shipping_fee)

      if (final_discount > 0) {
        result.ship_discount.push({
          amount: final_discount,
          note: ship_discount_config.name,
          config_id: ship_discount_config._id,
          is_applicable: true,
        })
      }
    }
  }

  return result
}

module.exports = {
  apply_gifts: _apply_gifts,
  get_sale_configs,
}
