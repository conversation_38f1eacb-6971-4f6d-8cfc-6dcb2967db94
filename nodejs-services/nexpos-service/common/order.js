const { HubMenuGroup, Hub, Site, SiteMenuGroup, HubStock } = require('../../.shared/database')
const { get_distance, get_location } = require('../../.shared/delivery/gmap')
const { send_slack_message } = require('../../.shared/slack')
const { find_item_in_menu_by_name } = require('../../.shared/helper');
const _ = require('lodash')
const moment = require('moment')
const { SELF_PICKUP } = require('../../.shared/const');

const get_item_stock = (item, stocks) => {
  let min_quantity = null
  if (!item.combo) {
    return null
  }
  for (let j = 0; j < item.combo.length; j++) {
    const stock_quantity = stocks[item.combo[j].code]
    if (stock_quantity) {
      if (min_quantity === null || stock_quantity < min_quantity) {
        min_quantity = stock_quantity
      }
    }
  }
  return min_quantity
}

const get_hubs_has_stocks = async ({ categories = [], items = [], address }, hub_ids) => {
  if (_.isEmpty(hub_ids)) return [];

  const hubs_stocks = await HubStock.find({ hub_id: hub_ids });
  const hubs = await Hub.find({ _id: { $in: hub_ids }, status: 'active' }).lean();

  const hub_stock_info = hub_ids.map(hub_id => {
    const hub_stocks = _.keyBy(_.filter(hubs_stocks, { hub_id }), 'code');
    const missing_items = items.filter(item => {
      const brand_item = find_item_in_menu_by_name(categories, item);
      if (!brand_item || brand_item.quantity_unlimited) return false;
      return get_item_stock(item, hub_stocks) <= Number(brand_item.min_quantity ?? 0);
    });
    return { hub_id, has_stock: _.isEmpty(missing_items), missing_items };
  });

  if (address) {
    const user_province = await _get_address_province(address);
    if (!user_province) return [];

    const hubs_with_distances = await Promise.all(
      hubs.map(async hub => {
        const hub_info = _.find(hub_stock_info, { hub_id: String(hub._id) });
        const distance = await get_distance(hub.address, address);
        return distance ? { ...hub, ...hub_info, distance } : null;
      })
    ).then(_.compact);

    return _.sortBy(hubs_with_distances, 'distance.value');
  } else {
    return hubs.map(hub => {
      const hub_info = _.find(hub_stock_info, { hub_id: String(hub._id) });
      return { ...hub, ...hub_info, distance: null };
    });
  }
};

const send_order_auto_assign_hub_slack_message = async ({ text, block }) => {
  const channel = 'C063ECT0PAA'
  await send_slack_message({ text: `${process.env.NODE_ENV === 'prod' ? 'PROD' : 'DEV'} env: ${text}}`, channel, block })
}

const _get_address_province = async (address) => {
  const location = await get_location(address)
  if (!location) {
    return null
  }
  const address_components = location.address_components
  const province = address_components.find((component) => component.types.includes('administrative_area_level_1'))
  return province ? province.long_name : null
}

const get_shipment_slots = () => {
  const maxDays = 3
  const slots = []

  const currentTime = moment().add(1, 'hours')

  for (let i = 0; i < maxDays; i++) {
    const currentDate = moment().clone().add(i, 'days').format('YYYY-MM-DD')
    let startTime = moment(`${currentDate} 09:00`, 'YYYY-MM-DD HH:mm')
    const endTime = moment(`${currentDate} 21:00`, 'YYYY-MM-DD HH:mm')

    if (startTime.isBefore(currentTime)) {
      startTime = currentTime.clone().startOf('hour')
    }

    while (startTime.isBefore(endTime)) {
      const slot = {
        date: startTime.format('YYYY-MM-DD'),
        from_time: startTime.format('HH:mm'),
        from_date_time: startTime.toISOString(),
        to_time: startTime.add(1, 'hours').format('HH:mm'),
        to_date_time: startTime.toISOString(),
        is_active: true,
      }
      slots.push(slot)
    }
  }
  return slots
}

const get_pickup_slots = () => {
  const slots = get_shipment_slots()

  const result = slots.map((v) => ({
    vendor: SELF_PICKUP.vendor,
    code: SELF_PICKUP.code,
    price: SELF_PICKUP.price,
    name: `Khung giờ ${v.from_time} -  ${v.to_time} , ngày ${moment(v.from_date_time).format('DD/MM')} `,
    description: 'Vui lòng đến cửa hàng để nhận hàng vào khung giờ này',
    options: SELF_PICKUP.options || [],
    raw: { ...SELF_PICKUP.raw, schedule: v },
  }))

  return result
}

module.exports = {
  get_hubs_has_stocks,
  send_order_auto_assign_hub_slack_message,
  get_item_stock,
  get_pickup_slots,
  get_shipment_slots,
}
