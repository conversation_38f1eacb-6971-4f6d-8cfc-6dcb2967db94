<!DOCTYPE html>
<html>

<head>
    <title>Label</title>
    <style>
        body {
            font-size: 32pt;
            font-family: Arial, sans-serif;
            /* max-width: 550px; */
            /* max-height: 320px; */
            margin: 0;
            padding: 2px;
            background-color: #ffffff;
            position: relative;
            box-sizing: border-box;
        }

        p {
            margin: 0;
            padding: 0;
        }

        .order-id {
            font-size: 32pt;
            font-weight: bold;
            text-align: left;
        }

        .order-source {
            font-size: 32pt;
            font-weight: bold;
            text-transform: capitalize;
        }

        .order-time {
            font-size: 30pt;
        }

        .divider {
            width: 100%;
            height: 2px;
            background-color: #222222;
            margin-top: 6pt;
            margin-bottom: 6pt;
        }

        .divider.--note {
            margin-top: 4pt;
            margin-bottom: 2pt;
        }

        .product-name {
            font-size: 32pt;
            font-weight: 700;
            text-align: left;
        }

        .product-quantity {
            font-size: 32pt;
            font-weight: 500;
        }

        .note {
            font-size: 30pt;
            text-align: left;
        }

        .option-list {
            padding-left: 4pt;
            margin-top: 2pt;
            margin-bottom: 2pt;
        }

        .option-item {
            margin: 0;
            padding: 0;
            font-size: 30pt;
            text-align: left;
            font-weight: medium;
        }

        .header {
            display: flex;
            align-items: center;
            flex-direction: row;
            padding-right: 4pt;
        }
        .header-left {
            flex-grow: 1;
        }

        @media print {
            @page {
                size: 5cm 3cm landscape;
                margin: 0;
            }

            body {
                margin: 0;
                padding: 2px;
            }

            .label {
                page-break-after: always;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="header-left">
            <p class="order-id">
                <%= order_id %>
                <span>
                     (<%= no %>/<%= total_items %>)
                </span>
            </p>
            <p class="order-time">
                <%= order_time %>
            </p>
        </div>
        <p class="order-source">
            <%= source %>
        </p>
    </div>
    <div class="divider"></div>
    <p class="product-name">
        <%= item.name %>
    </p>

    <% if (Array.isArray(options) && options.length > 0) { %>
        <div class="option-list">
            <% options.forEach(function(option) { %>
                <p class="option-item">
                    <%= option %>
                </p>
            <% }) %>
        </div>
    <% } %>
    <% if (typeof note === 'string' && note.length > 0) { %>
        <div>
            <div class="divider --note"></div>
            <p class="note">
                <%= note %>
            </p>
        </div>
    <% } %>
</body>

</html>