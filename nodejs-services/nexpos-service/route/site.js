const { Site, User, PrintQueue, <PERSON><PERSON>, <PERSON>, TokenAccount, PrintQueueV2 } = require('../../.shared/database')
const baemin = require('../../.shared/merchant/baemin')
const shopee = require('../../.shared/merchant/shopee')
const gojek = require('../../.shared/merchant/gojek')
const be = require('../../.shared/merchant/be')
const grab = require('../../.shared/merchant/grab')
const { map_working_hour, reverse_map_working_hour, map_special_working_hour } = require('../../.shared/merchant/mapping')
const _ = require('lodash')
const xlsx = require('xlsx');
const moment = require('moment');
const { upload_file } = require('../../.shared/storage')
const { is_site_apply_voucher } = require('./voucher')
const { get_token_by_site, get_offline_token_by_site } = require('../../.shared/token_account')
const { MERCHANT_INFO, SELF_PICKUP, TAKE_AWAY } = require('../../.shared/const')

exports.get_site_list = async (req, res) => {
    let filter = {
        deleted_at: { $exists: false }
    };
    if (req.query.brand_id) {
        filter.brand_id = req.query.brand_id
    }
    if (req.query.active) {
        filter.active = req.query.active
    }
    if (req.query.type) {
        filter.type = { $in: req.query.type }
    }
    if (req.query.apply_ph_commission) {
        filter.partner_hub_tier = { $exists: true }
    }
    if (req.query.use_core_product) {
        filter.use_core_product = req.query.use_core_product
    }
    if (req.query.name) {
        const escaped_name = req.query.name.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
        filter.$or = [
            { name: { $regex: new RegExp(`.*${escaped_name}.*`, 'i') } },
            { code: { $regex: new RegExp(`.*${escaped_name}.*`, 'i') } }
        ];
    }

    if (req.user && !req.permissions?.includes('system')) {
        filter._id = req.user.sites
    }

    const site_paginate = await Site.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 200), // Max 100 docs
        sort: { name: 1 },
        customLabels: { docs: 'data' },
        // lean: true,
    });

    const hubs = await Hub.find({ _id: site_paginate.data.map(v => [v.hub_id, ...(v.hub_ids || [])]).flat() })
    const brands = await Brand.find({ _id: site_paginate.data.map(v => v.brand_id) })

    const hubMap = _.keyBy(hubs, "_id")
    const brandMap = _.keyBy(brands, "_id")
    const data = []
    for (let i = 0; i < site_paginate.data.length; i++) {
        const apply_voucher = is_site_apply_voucher(site_paginate.data[i])

        data.push({
            ...site_paginate.data[i].toObject(),
            apply_voucher,
            hubs: site_paginate.data[i].hub_ids?.map(v => hubMap[v]) || [],
            hub: hubMap[site_paginate.data[i].hub_id],
            brand: brandMap[site_paginate.data[i].brand_id],
        })
    }

    res.json({
        success: true,
        ...site_paginate,
        data,
    });
}

exports.get_site = async (req, res) => {
    let filter = {}
    if (req.query.code) {
        filter.code = [req.query.code, req.query.code.toLowerCase(), req.query.code.toUpperCase()]
    }
    if (req.query.id) {
        filter._id = req.query.id
    }

    // Force for custom site
    if (req.headers['origin']?.includes('ooo.com.vn')) {
        filter = { code: 'cskh.seller' }
    }
    if (req.headers['origin']?.includes('nexpos.io') && req.query.code === '') {
        filter = { code: 'NNDD' }
    }
    const site = await Site.findOne(filter)
    if (!site)
        return res.json({ success: false, error: 'site_id_not_found' })

    let result = site.toObject()
    const brand = await Brand.findById(site.brand_id)
    const zalo_token = brand.getToken('zalo')
    const apply_voucher = is_site_apply_voucher(site)


    const self_pickup_token = site.getToken('self_pickup')
    if (self_pickup_token?.settings?.service_types?.includes('pick_up')) {
        result.self_pickup = SELF_PICKUP
    }
    if (self_pickup_token?.settings?.service_types?.includes('take_away')) {
        result.take_away = TAKE_AWAY
    }

    res.json({
        success: true,
        data: {
            ...result,
            tokens: undefined,
            banners: brand?.banners || [],
            apply_voucher,
            brand: {
                zalo_oa_url: zalo_token.site_data ? JSON.parse(zalo_token.site_data).zalo_oa_url : ""
            }
        }
    });
}

// Create a new Site
exports.create_site = async (req, res) => {
    const { brand_id, hub_id, type, name, code, address, address_obj, description, tokens, active, auto_confirm, auto_print, printer_ip, slack_channel, zalo_group, apply_commission, apply_gift, hub_ids, partner_hub_tier, use_core_product, enable_local_order_shipping, is_head_site, preparation_time_per_order } = req.body;

    const is_site_exist = await Site.findOne({ code })
    if (is_site_exist) {
        return res.json({ success: false, error: 'site_code_exist' })
    }

    try {
        const is_he_site = type === 'partner'
        const result = await Site.create({
            brand_id,
            hub_id,
            hub_ids,
            type,
            name,
            code,
            address,
            address_obj,
            description,
            tokens,
            active,
            auto_confirm,
            auto_print,
            printer_ip,
            slack_channel,
            zalo_group,
            partner_hub_tier,
            use_core_product,
            enable_local_order_shipping,
            is_head_site,
            preparation_time_per_order,
            ...(is_he_site ? { he_id: req.user._id, apply_commission, apply_gift } : {}),
        });

        await User.findByIdAndUpdate(req.user._id, { $addToSet: { sites: result._id } })

        if (is_head_site === true) {
            await Site.updateMany({ brand_id, _id: { $ne: result._id } }, { is_head_site: false })
        }

        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

// Update an existing site
exports.update_site = async (req, res) => {
    const { brand_id, hub_id, name, code, address, address_obj, description, tokens, active, auto_confirm, auto_print, printer_ip, slack_channel, zalo_group, apply_commission, apply_gift, hub_ids, partner_hub_tier, use_core_product, enable_local_order_shipping, is_head_site, preparation_time_per_order } = req.body;

    const site = await Site.findById(req.params.site_id)
    if (!site) {
        return res.json({ success: false, error: 'site_id_not_found' })
    }

    try {
        const is_he_site = site.type === 'partner'
        const result = await Site.findByIdAndUpdate(
            req.params.site_id,
            { brand_id, hub_id, hub_ids, name, code, address, address_obj, description, tokens, active, auto_confirm, auto_print, printer_ip, slack_channel, zalo_group, partner_hub_tier, use_core_product, enable_local_order_shipping, is_head_site, preparation_time_per_order, ...(is_he_site ? { apply_commission, apply_gift } : {}) },
            { new: true }
        );
        if (is_head_site === true) {
            await Site.updateMany({ brand_id: site.brand_id, _id: { $ne: site._id } }, { is_head_site: false })
        }
        res.json({
            success: true,
            data: result,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

// Delete a site
exports.delete_site = async (req, res) => {
    try {
        await Site.findOneAndUpdate(req.params.site_id, { deleted_at: new Date() });
        res.json({
            success: true,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.update_store_open = async (req, res) => {
    try {
        const site = await Site.findById(req.params.site_id)
        const { apps, duration } = req.body

        if (process.env.USE_MERCHANT_APPS !== "true") {
            return res.json({
                success: true,
                data: site.toJSON(),
            })
        }
        const merchant_functions = {
            shopee: shopee,
            shopee_fresh: shopee,
            // gojek: gojek,
            grab: grab,
            grab_mart: grab,
            be: be,
        }

        for (const source of Object.keys(apps)) {
            const status = apps[source] ? "close" : "open"
            const token = await get_token_by_site(site, source)
            const merchant_function = merchant_functions[source]
            if (merchant_function) {
                await merchant_function.update_store_status(token, { status, duration })
            }
        }

        site.pause_apps = { ...site.pause_apps, ...apps }
        await site.save()

        res.json({
            success: true,
            data: site.toJSON(),
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_store_opening_hours = async (req, res) => {
    try {
        const site = await Site.findById(req.params.site_id)

        let new_working_hour = null
        const merchant_functions = {
            shopee: shopee.get_opening_hour,
            shopee_fresh: shopee.get_opening_hour,
            // gojek: gojek.get_opening_hour,
            grab: grab.get_opening_hour,
            grab_mart: grab.get_opening_hour,
        }
        const merchant_function = merchant_functions[req.query.source]
        if (merchant_function) {
            const token = await get_token_by_site(site, req.query.source)
            const working_hours = await merchant_function(token)
            new_working_hour = map_working_hour(req.query.source, working_hours)
            if (new_working_hour !== null) {
                site.working_hours = new_working_hour
                await site.save()
            }
        }

        res.json({
            success: true,
            data: new_working_hour,
        });


    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.sync_store_opening_hours = async (req, res) => {
    try {
        const site = await Site.findById(req.params.site_id)
        let { working_hours, sources } = req.body

        if (process.env.USE_MERCHANT_APPS !== "true") {
            return res.json({
                success: true,
            })
        }
        if (!sources) {
            sources = ['shopee', 'shopee_fresh', 'grab', 'grab_mart', 'be']
        }
        const merchant_functions = {
            shopee: shopee.update_opening_hour,
            shopee_fresh: shopee.update_opening_hour,
            // gojek: gojek.update_opening_hour,
            grab: grab.update_opening_hour,
            grab_mart: grab.update_opening_hour,
            be: be.update_opening_hour,
        }
        for (const [source, merchant_function] of Object.entries(merchant_functions)) {
            if (!sources.includes(source)) continue
            const token = await get_token_by_site(site, source)
            const merchant_working_hours = reverse_map_working_hour(source, working_hours)
            await merchant_function(token, merchant_working_hours)
        }
        site.working_hours = working_hours
        await site.save()
        res.json({
            success: true,
        });


    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_store_special_opening_hours = async (req, res) => {
    try {
        const site = await Site.findById(req.params.site_id)

        const merchant_functions = {
            shopee: shopee.get_special_hour,
            shopee_fresh: shopee.get_special_hour,
            grab: grab.get_special_hour,
            grab_mart: grab.get_special_hour,
        }
        let special_working_hours = []
        for (const [source, merchant_function] of Object.entries(merchant_functions)) {
            const token = await get_token_by_site(site, source)
            const working_hours = await merchant_function(token)
            if (working_hours?.length > 0) {
                const new_working_hour = map_special_working_hour(source, working_hours)
                special_working_hours.push(...new_working_hour)
            }
        }

        site.special_working_hours = special_working_hours
        await site.save()
        res.json({
            success: true,
            data: site.special_working_hours
        });


    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


exports.get_site_print_list = async (req, res) => {
    try {
        const { site_id } = req.params
        const { version, hub_code } = req.query
        const hub = await Hub.findOne({ code: hub_code })
        const print_list = await PrintQueueV2.find({ hub_id: hub._id, site_id: req.params.site_id, status: 'created' }).lean()
        if (print_list.length > 0) {
            await PrintQueueV2.updateMany({ _id: print_list.map(v => v._id) }, { $set: { status: 'printed' } })
        }

        res.json({
            data: print_list,
            success: true,
        });

    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_hub_print_list = async (req, res) => {
    try {
        const { hub_id } = req.params
        const hub = await Hub.findById(hub_id)
        const print_list = await PrintQueueV2.find({ hub_id: hub._id, status: 'created', created_at: { $gt: moment().add(-30, 'minutes') } }).lean()
        if (print_list.length > 0) {
            await PrintQueueV2.updateMany({ _id: print_list.map(v => v._id) }, { $set: { status: 'printed' } })
        }

        res.json({
            data: print_list,
            success: true,
        });

    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_hub_print_list_v2 = async (req, res) => {
    try {
        const { code } = req.query
        const hub = await Hub.findOne({ code })
        const print_list = await PrintQueueV2.find({
            hub_id: hub._id,
            status: 'created',
            created_at: { $gt: moment().add(-1, 'hour') }
        }).lean()

        if (print_list.length > 0) {
            await PrintQueueV2.updateMany({ _id: print_list.map(v => v._id) }, { $set: { status: 'printed' } })
        }

        res.json({
            data: print_list,
            success: true,
        });

    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.update_site_print_queue = async (req, res) => {
    try {
        const { status, error_message } = req.body
        const { site_id, print_id } = req.params
        await PrintQueueV2.findByIdAndUpdate(print_id, { status, error_message })

        res.json({
            success: true,
        });

    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}

exports.get_site_open_status = async (req, res) => {
    try {
        const site = await Site.findById(req.params.site_id)

        if (process.env.USE_MERCHANT_APPS !== "true") {
            return res.json({
                pause_apps: {
                    shopee: false,
                    shopee_fresh: false,
                    be: false,
                    gojek: false,
                    grab: false,
                    grab_mart: false,
                    baemin: false,
                },
                success: true,
            })
        }

        const pause_app = {}
        let new_working_hour = site.working_hours

        const merchant_functions = {
            shopee: shopee,
            shopee_fresh: shopee,
            grab: grab,
            grab_mart: grab,
            be: be,
        }
        for (const [source, merchant_function] of Object.entries(merchant_functions)) {
            const token = await get_token_by_site(site, source)
            const open_status = await merchant_function.get_open_status(token)
            if (open_status !== null) pause_app[source] = !open_status

            if (open_status) {
                const working_hours = await merchant_function.get_opening_hour(token)
                new_working_hour = map_working_hour(source, working_hours)
            }
        }

        site.pause_apps = pause_app
        site.working_hours = new_working_hour
        await site.save()

        res.json({
            success: true,
            data: site,
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}


exports.get_site_by_id = async (req, res) => {
    const site = await Site.findById(req.params.site_id).lean()

    const brand = await Brand.findOne({ _id: site.brand_id }, { tokens: 0 })
    const hubs = await Hub.find({
        $or: [
            { _id: site.hub_id },
            { _id: site.hub_ids },
        ]
    })
    site.brand = brand
    site.hub = hubs.find(v => String(v._id) === site.hub_id)
    site.hubs = hubs.filter(v => site.hub_ids?.includes(String(v._id)))
    // site.getTokensForUser()
    res.json({
        success: true,
        data: site,
    });
}

exports.get_brand_token_headers = async (req, res) => {
    // TODO: Get config from brand
    res.json({
        success: true,
        data: [{
            source: 'zalo',
            name: 'Zalo',
            fields: [{
                key: 'username',
                name: 'App Id',
            }, {
                key: 'password',
                name: 'App Secret',
            }, {
                key: 'site_id',
                name: 'OA Id',
            }, {
                key: 'access_token',
                name: 'Access Token',
            }, {
                key: 'refresh_token',
                name: 'Refresh token',
            }]
        }, {
            source: 'website_o_o_o',
            name: 'Website Ò Ó O',
            fields: [{
                key: 'username',
                name: 'Username',
            }, {
                key: 'password',
                name: 'Password',
            }]
        }],
    });
}

exports.get_site_errors = async (req, res) => {
    if (!req.permissions?.includes('system')) {
        return res.json({
            success: true,
            data: [],
        });
    }
    const token_accounts = await TokenAccount.find({
        working: false,
        source: ['grab', 'shopee', 'grab_express', 'ahamove'],
        last_working_at: { $lt: moment().add(-10, 'minutes').toDate() }
    }).lean()
    const result = token_accounts.map(v => `Lỗi kết nối ứng dụng: ${MERCHANT_INFO[v.source]?.label || v.source} - ${v.token_code}`)
    res.json({
        success: true,
        data: result,
    });
}

exports.get_site_exports = async (req, res) => {
    try {
        let filter = {}

        if (req.query.name) {
            filter.name = { $regex: new RegExp(`.*${req.query.name}.*`, 'i') };
        }

        if (req.user && !req.permissions?.includes('system')) {
            filter._id = req.user.sites
        }

        const sites = await Site.find(filter)
        const brands = await Brand.find({ _id: sites.map(v => v.brand_id) })
        const hubs = await Hub.find({ _id: sites.map(v => v.hub_id) })

        const brandMap = _.keyBy(brands, "_id")
        const hubMap = _.keyBy(hubs, "_id")
        const sheet_data = sites.map(v => {
            return {
                'Tên cửa hàng': v.name,
                'Mã cửa hàng': v.code,
                'Địa chỉ': v.address,
                'Thương hiệu': brandMap[v.brand_id]?.name,
                'Khu vực hub': hubMap[v.hub_id]?.name,
                'Loại cửa hàng': v.type,
                'Trạng thái': v.active ? 'Hoạt động' : 'Ngừng hoạt động',
                'Tự động xác nhận': v.auto_confirm ? 'Có' : 'Không',
                'Tự động in': v.auto_print ? 'Có' : 'Không',
                'Áp dụng hoa hồng': v.apply_commission ? 'Có' : 'Không',
                'Áp dụng quà tặng': v.apply_gift ? 'Có' : 'Không',
                'Shopee': v.getToken('shopee')?.access_token ? 'Có: ' + v.getToken('shopee')?.username : 'Không',
                'Shopee Fresh': v.getToken('shopee_fresh')?.access_token ? 'Có: ' + v.getToken('shopee_fresh')?.username : 'Không',
                'Gojek': v.getToken('gojek')?.access_token ? 'Có: ' + v.getToken('gojek')?.username : 'Không',
                'Grab': v.getToken('grab')?.access_token ? 'Có: ' + v.getToken('grab')?.username : 'Không',
                'Grab Mart': v.getToken('grab_mart')?.access_token ? 'Có: ' + v.getToken('grab_mart')?.username : 'Không',
                'BE': v.getToken('be')?.access_token ? 'Có: ' + v.getToken('be')?.username : 'Không',
            }
        })

        const sheet = xlsx.utils.json_to_sheet(sheet_data);

        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, sheet, 'Report');
        const buff = xlsx.write(workbook, { type: 'buffer' });

        const file = await upload_file({ bucket: 'nexpos-files', key: `reports/Báo_cáo_${Date.now()}.xlsx`, buff })
        res.json({
            success: true,
            data: file
        })
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message,
        });
    }
}