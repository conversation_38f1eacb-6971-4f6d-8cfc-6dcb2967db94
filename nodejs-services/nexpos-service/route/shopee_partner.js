const { Hub, Site, User, BrandMenu, BrandMasterData, VendorCallback, Order, HubStock, Brand, VendorHubStock } = require('../../.shared/database')
const { GRAB_CATEGORIES } = require('../../.shared/merchant/grab_mart_official')
const _ = require('lodash')
const moment = require('moment')
const crypto = require('crypto')
const axios = require('../../.shared/axios')
const { text_slugify, deep_merge_object, calculate_nutifood_item_quantity, find_item_in_menu_by_name, name_to_id, calculate_item_min_quantity } = require('../../.shared/helper');
const jwt = require('jsonwebtoken')
const { map_order, map_menu } = require('../../.shared/merchant/mapping')
const shopee = require('../../.shared/merchant/shopee');
const shopee_official = require('../../.shared/merchant/shopee_official');
const { get_token_by_site } = require('../../.shared/token_account')
const nutifood = require('../../.shared/merchant/nutifood')
const { get_brand_menu_from_google_sheet } = require('../../.shared/googlesheet/template')
const { read_sheet } = require('../../.shared/googlesheet')
const { lswap_queue_by_id } = require('../../.shared/redis_queue')

let router = {};

const CLIENT_ID = "shopee_fresh";
const CLIENT_SECRET = "7X9bD-KcJLszYGx2";

router.get_access_token = async (req, res) => {
    const { client_id, client_secret, grant_type, scope } = req.body;

    if (client_id !== CLIENT_ID || client_secret != CLIENT_SECRET) {
        return res.status(401).json({
            success: false,
            message: "invalid_client_id_or_client_secret",
        });
    }
    const access_token = jwt.sign({ username: CLIENT_ID }, CLIENT_SECRET, { expiresIn: '7d' })


    res.json({
        access_token: access_token,
        token_type: "Bearer",
        expires_in: 7 * 60 * 60,
    })
}

// Get mart menu from shopee apply to shopee
router.get_mart_menu_v2 = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }

    const site = await Site.findOne({ code: partnerMerchantID });
    if (!site) {
        res.status(400).json({
            success: false,
            message: "invalid_partner_merchant_id",
        });
        return;
    }
    const hub = await Hub.findOne({ _id: site.hub_id })
    const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }).lean()

    const menu_mapping = await get_brand_menu_from_google_sheet(site.brand_id, 'shopee_fresh')

    if (hub.inventory_source === 'nutifood') {
        const vendor_hub_stock = await VendorHubStock.findOne({ hub_id: hub._id, vendor: 'nutifood' }).lean()
        const stock_items = vendor_hub_stock?.stocks ?? []
        const hub_stocks = await HubStock.find({ hub_id: hub._id }).lean()

        // Override quantity base on hub stock config
        for (const stock_item of stock_items) {
            const hub_stock = hub_stocks.find(v => v.code === stock_item.code)
            if (hub_stock) {
                if (hub_stock.locked_status === 'alway_active') {
                    stock_item.quantity = 1000000
                }
                if (hub_stock.locked_status === 'alway_inactive') {
                    stock_item.quantity = 0
                }
            }
        }
        for (const category of menu_mapping.categories) {
            for (const item of category.items) {
                const menu_item = find_item_in_menu_by_name(brand_menu.categories, item)
                if (menu_item?.combo?.length > 0) {
                    const min_quantity = calculate_item_min_quantity(menu_item, stock_items)
                    if (min_quantity !== null) {
                        item.active = min_quantity > menu_item.quantity_minimum
                    } else {
                        item.active = false
                    }
                } else {
                    item.active = false
                }
            }
        }
    } else {
        for (const category of menu_mapping.categories) {
            for (const item of category.items) {
                item.active = true
            }
        }
    }

    const result = {
        merchantID: merchantID,
        partnerMerchantID: partnerMerchantID,
        currency: {
            code: "VND",
            symbol: "₫",
            exponent: 0,
        },
        sections: [{
            "id": "1",
            "name": "Menu",
            "serviceHours": {
                "mon": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                },
                "tue": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                },
                "wed": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                },
                "thu": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                },
                "fri": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                },
                "sat": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                },
                "sun": {
                    "openPeriodType": "OpenPeriod",
                    "periods": [
                        {
                            "startTime": "8:30",
                            "endTime": "20:30"
                        }
                    ]
                }
            },
            categories: menu_mapping.categories.map((category, ci) => {
                return {
                    id: category.id,
                    name: category.name,
                    sort_type: 1,
                    sequence: ci + 1,
                    availableStatus: "AVAILABLE",
                    items: category.items?.map((v, vi) => {
                        const modify_groups = menu_mapping.option_categories.filter(v2 => v2.category_ids?.includes(v.id));
                        return {
                            id: v.id,
                            name: v.name,
                            sequence: vi + 1,
                            availableStatus: v.active ? "AVAILABLE" : "UNAVAILABLE",
                            description: v.description,
                            price: v.price,
                            photos: [v.image],
                            modifierGroups: modify_groups.map((v2, v2i) => {
                                return {
                                    "id": v2.id,
                                    "name": v2.name,
                                    "sequence": v2i + 1,
                                    "availableStatus": "AVAILABLE",
                                    "selectionRangeMin": v2.rule.min_quantity,
                                    "selectionRangeMax": v2.rule.max_quantity,
                                    "modifiers": v2.options?.map((v3, v3i) => {
                                        return {
                                            "id": v3.id,
                                            "name": v3.name,
                                            "sequence": v3i + 1,
                                            "availableStatus": v3.active ? "AVAILABLE" : "UNAVAILABLE",
                                            "price": v3.price,
                                        }
                                    }) ?? []
                                }
                            }) || [],
                        }
                    }).filter(Boolean)
                }
            }).filter(Boolean)
        }],


    }

    return res.json(result);
}



router.clone_site_campaign = async (req, res) => {
    const { authorization: Authorization } = req.headers;
    const { merchantID, partnerMerchantID } = req.query;
    if (!Authorization || !merchantID || !partnerMerchantID) {
        res.status(400).json({
            success: false,
            message: "missing_headers",
        });
        return;
    }


}

router.webhook = async (req, res) => {
    await VendorCallback.create({
        vendor: 'shopee',
        type: 'order',
        headers: req.headers,
        request_data: req.body,
        url: req.url,
        method: req.method,
    })
    const { order_code, restaurant_id } = req.body
    let merchant_order = await shopee_official.get_order_detail({ site_id: restaurant_id }, order_code)
    const site = await Site.findOne({ code: merchant_order.restaurant.partner_restaurant_id })
    let shopee_store = await shopee_official.get_store({ nexpos_site_id: site.code })

    const order_source = shopee_store.restaurants[0].foody_service === 'FOOD' ? 'shopee' : 'shopee_fresh'
    const order_status = {
        '5': 'PENDING', // Order is assigned/sent to merchant, After merchants responses 200, order is marked as received by merchant
        '6': 'DOING',
        '3': 'DOING',
        '1': 'PICK',
        '2': 'FINISH',
        '7': 'CANCEL',
        '8': 'CANCEL',
    }[merchant_order.status]
    const token = await get_token_by_site(site, order_source)

    let data = {
        official_data: merchant_order,
    }
    const merchant_order_2 = await shopee.get_order_detail(token, order_code)
    if (merchant_order_2) {
        merchant_order_2.official_data = merchant_order
        data = merchant_order_2
    }

    const db_order = await Order.findOne({ order_id: order_code })
    data = deep_merge_object(db_order?.data || {}, data)

    const data_mapping = map_order(order_source, data)
    const hub = await Hub.findOne({ _id: site.hub_id }, { name: 1, inventory_source: 1 })
    const result = await Order.findOneAndUpdate({ order_id: order_code }, {
        site_id: site._id,
        hub_id: site.hub_id,
        source: order_source,
        order_id: order_code,
        status: order_status,
        data: data,
        data_mapping,
        $push: { data_history: data },
    }, { upsert: true, new: true })

    if (hub.inventory_source === 'nutifood' && (result.isNew || order_status === 'CANCEL')) {
        await lswap_queue_by_id('cron_job:nutifood_hub_stock', String(hub._id))
    }

    res.json({ success: true });
}

module.exports = router;