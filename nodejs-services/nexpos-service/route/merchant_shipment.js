const ejs = require('ejs')
const axios = require('axios')
const _ = require('lodash')
const fs = require('fs')
const AWS = require('aws-sdk')
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const { Site, Order, Hub, PrintQueue, SiteOrderIndex, Brand, HubStock, HubStockHistory, OrderShipment } = require('../../.shared/database')
const { get_hubs_has_stocks, get_pickup_slots, get_shipment_slots } = require('../common/order')

const ahamove = require('../../.shared/delivery/ahamove');
const ghn = require('../../.shared/delivery/ghn');
const grab_express = require('../../.shared/delivery/grab_express');
const viettel_post = require('../../.shared/delivery/viettel_post');
const token_account = require('../../.shared/token_account');
const { SELF_PICKUP } = require('../../.shared/const')

exports.get_new_order_shipments = async (req, res, next) => {
    const { site_id } = req.params
    const { hub_id, customer_phone, customer_name, customer_address } = req.body
    const site = await Site.findById(site_id)
    // const brand = await Brand.findById(site.brand_id)

    if (!customer_phone || !customer_name || !customer_address) {
        return res.json({ success: false, error: 'missing_customer_nor_phone_name_address' })
    }

    const hub_with_stocks = await get_hubs_has_stocks({
        categories: [],
        items: [],
        address: customer_address,
    }, site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id])
    if (hub_with_stocks.length === 0) {
        return res.json({ success: false, error: 'no_hub_with_stock' })
    }

    const hub = hub_with_stocks?.find(hub => hub.has_stock)
    const shipment = {
        from: {
            address: hub.address,
            name: hub.name,
            phone: hub.phone,
        }, to: {
            address: customer_address,
            name: customer_name,
            phone: customer_phone,
        }
    }

    const result = await this.get_site_shipment_from_address(site, shipment)
    res.json({
        success: true,
        data: result
    })
}

exports.get_site_shipment_from_address = async (site, { from, to }) => {
    const result = {
        // pick_up: [],
        instant_ship: [],
        same_day_ship: [],
        two_hour_ship: [],
        schedule_ship: [],
        provice_ship: [],
    }
    const ship_address = { from, to }
    const [ahamove_shipments, grab_express_shipments, viettel_post_shipments] = await Promise.all([
        ahamove.get_shipments(await token_account.get_token_by_site(site, 'ahamove'), ship_address),
        grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express'), ship_address, 'grab_express'),
        viettel_post.get_shipments(await token_account.get_token_by_site(site, 'viettel_post'), ship_address),
    ])

    const shop_shipments = [SELF_PICKUP]

    // result.instant_ship.push(...ahamove_shipments.filter(v => ['SGN-BIKE'].includes(v.code)))
    result.instant_ship.push(...grab_express_shipments.filter(v => ['INSTANT'].includes(v.code)))

    // result.same_day_ship.push(...ahamove_shipments.filter(v => ['SGN-SAMEDAY'].includes(v.code)))
    // result.same_day_ship.push(...grab_express_shipments.filter(v => ['SAME_DAY'].includes(v.code)))

    result.two_hour_ship.push(...ahamove_shipments.filter(v => ['SGN-BIKE'].includes(v.code)))
    // result.two_hour_ship.push(...grab_express_2h_shipments.filter(v => ['INSTANT'].includes(v.code)))

    // Sort instant_ship by lowest price and get the first item
    result.instant_ship = result.instant_ship.sort((a, b) => a.price - b.price).filter(v => v.price > 0).slice(0, 1)
    // result.same_day_ship = result.same_day_ship.sort((a, b) => a.price - b.price).filter(v => v.price > 0).slice(0, 1)
    result.two_hour_ship = result.two_hour_ship.sort((a, b) => a.price - b.price).filter(v => v.price > 0).slice(0, 1)

    result.provice_ship.push(...viettel_post_shipments)

    if (result.two_hour_ship.length > 0) {
        const slots = get_shipment_slots()
        const choice_slot = result.two_hour_ship[0]
        result.schedule_ship = slots.map(v => ({
            vendor: choice_slot.vendor,
            code: choice_slot.code,
            price: choice_slot.price,
            name: `${v.from_time} -  ${v.to_time}, ${moment(v.from_date_time).format('DD/MM')} `,
            description: "Shop sẽ liên hệ và giao hàng theo lịch hẹn của bạn",
            options: choice_slot.options || [],
            raw: { ...choice_slot.raw, schedule: v },
        }))
    }

    delete result.two_hour_ship
    // delete result.same_day_ship

    const current_time = moment().utcOffset('+07:00');
    const before_8am = current_time.isBefore(moment().set({ hour: 8, minute: 0, second: 0 }));
    const after_9pm = current_time.isAfter(moment().set({ hour: 21, minute: 0, second: 0 }));
    if (before_8am || after_9pm) {
        result.instant_ship = []
    }
    return result
}

exports.get_order_shipments = async (req, res, next) => {
    const { order_id } = req.params
    const { dimensions } = req.query
    const order = await Order.findOne({ order_id }).lean()
    const site = await Site.findById(order.site_id)

    const is_delivery = order.shipment?.service?.vendor !== 'pick_up'
    if (is_delivery) {
        const shipment = order.shipment
        shipment.dimensions = _.mapValues(dimensions, Number);
        const ahamove_shipments = await ahamove.get_shipments(await token_account.get_token_by_site(site, 'ahamove'), shipment)
        const grab_shipments = await grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express'), shipment, 'grab_express')
        const grab_2h_shipments = await grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express_2h'), shipment, 'grab_express_2h')
        const viettel_post_shipments = await viettel_post.get_shipments(await token_account.get_token_by_site(site, 'viettel_post'), shipment)

        const result = [
            ...ahamove_shipments,
            ...grab_shipments,
            ...grab_2h_shipments,
            ...viettel_post_shipments,
        ]
        return res.json({
            success: true,
            data: result.filter(v => v.price > 0)
        })
    }

    const slots = get_pickup_slots()
    res.json({
        success: true,
        data: slots
    })
}

exports.get_order_shipment_histories = async (req, res, next) => {
    let filter = {}
    if (req.query.search) {
        filter = {
            $or: [
                { order_id: { $regex: req.query.search, $options: 'i' } },
                { vendor: { $regex: req.query.search, $options: 'i' } },
                { shipment_id: { $regex: req.query.search, $options: 'i' } },
            ]
        }
    }
    const order_shipments = await OrderShipment.paginate(filter, {
        page: Number(req.query.page || 1),
        limit: Number(req.query.limit || 100), // Max 100 sites
        sort: { created_at: -1 },
        customLabels: { docs: 'data' },
    })

    res.json({
        success: true,
        ...order_shipments,
    })
}

exports.create_order_shipments = async (req, res, next) => {
    const { order_id } = req.params
    const { vendor, dimensions, is_sub_shipment, raw } = req.body
    const order = await Order.findOne({ order_id })
    const site = await Site.findById(order.site_id)
    const hub = await Hub.findById(order.hub_id)

    // if (order.shipment?.shipment_id && order.shipment?.status !== 'ORDER_CREATING' && order.shipment?.status !== 'CANCELLED') {
    //     return res.json({
    //         success: false,
    //         error: 'order_shipment_exist'
    //     })
    // }
    order.shipment.vendor = vendor
    order.shipment.service = req.body
    order.shipment.status = 'ORDER_CREATED'

    if (raw?.schedule) {
        order.shipment.schedule = raw.schedule
    }

    const total_paid = _.sumBy(order.data_mapping.payments?.filter(v =>
        v.status === 'COMPLETED'
        && v.method !== 'CASH'
        && v.method !== 'COD'
    ) || [], 'total') || 0

    let total_remain = order.data_mapping.total_for_biz - total_paid
    if (total_remain < 0) { total_remain = 0 }
    if (is_sub_shipment) {
        total_remain = 0
    }

    const ship_services = {
        'ahamove': ahamove.create_order,
        'grab_express': grab_express.create_order,
        'grab_express_2h': grab_express.create_order,
        'viettel_post': viettel_post.create_order,
    }
    const ship_service = ship_services[vendor]
    if (ship_service) {
        const token = await token_account.get_token_by_site(site, vendor)
        const ship = await ship_service(token, {
            promo_code: order.shipment.promo_code,
            dishes: order.data_mapping.dishes,
            from: {
                address: hub.address,
                phone: hub.phone,
                name: hub.name,
            },
            to: order.shipment.to,
            service: order.shipment.service,
            cod: total_remain,
            note: `Đến lấy hàng, đọc mã đơn hàng: ${order.order_id} cho thu ngân`,
            tracking_number: order.order_id,
            schedule_order_time: null,
            dimensions: dimensions,
        })
        order.shipment.shipment_id = ship.shipment_id
        order.shipment.tracking_url = ship.tracking_url
        order.shipment.price = ship.price
        order.shipment.status = 'ORDER_CREATED'
        await OrderShipment.findOneAndUpdate({ shipment_id: ship.shipment_id }, {
            order_id: order.order_id,
            is_sub_shipment,
            cod: total_remain,
            vendor: vendor,
            from_name: hub.name,
            from_phone: hub.phone,
            from_address: hub.address,
            to_name: order.shipment.to?.name,
            to_phone: order.shipment.to?.phone,
            to_address: order.shipment.to?.address,
            tracking_url: ship.tracking_url,
            price_for_user: order.shipment?.price ?? ship.price,
            price: ship.price,
            status: 'ORDER_CREATED',
            webhooks: [ship],
        }, { upsert: true })
    }

    order.markModified('shipment')
    await order.save()

    res.json({
        success: true,
        data: order
    })
}

exports.create_order_manual_shipments = async (req, res, next) => {
    const { order_id } = req.params
    const {
        vendor,
        cod,
        price,
        shipment_id,
        to_address,
        to_name,
        to_phone,
    } = req.body

    const order = await Order.findOne({ order_id })
    const hub = await Hub.findById(order.hub_id)

    await OrderShipment.create({
        is_manual: true,
        cod,
        order_id: order.order_id,
        vendor: vendor,
        shipment_id: shipment_id,
        from_name: hub.name,
        from_phone: hub.phone,
        from_address: hub.address,
        to_name,
        to_phone,
        to_address,
        price_for_user: price,
        price: price,
        status: 'COMPLETED',
        webhooks: [],
    })

    order.shipment.shipment_id = shipment_id
    order.shipment.note = `Vận đơn ngoài hệ thống: ${vendor} ${shipment_id}`
    order.shipment.vendor = vendor
    order.shipment.status = 'COMPLETED'
    order.markModified('shipment')
    await order.save()

    res.json({
        success: true,
        data: order
    })
}

exports.cancel_order_shipments = async (req, res, next) => {
    const { order_id } = req.params
    const { shipment_id, vendor } = req.body
    const order = await Order.findOne({ order_id })
    const site = await Site.findById(order.site_id)
    if (shipment_id) {
        const merchant_functions = {
            'ahamove': ahamove.cancel_order,
            'grab_express': grab_express.cancel_order,
            'grab_express_2h': grab_express.cancel_order,
            'viettel_post': viettel_post.cancel_order,
        }
        const order_shipment = await OrderShipment.findOne({ shipment_id })
        const merchant_function = merchant_functions[vendor]
        if (merchant_function) {
            const token = await token_account.get_token_by_site(site, vendor)
            const cancel_resp = await merchant_function(token, {
                shipment_id: shipment_id,
                order_id: order.order_id,
            })
            if (cancel_resp.success) {
                order_shipment.status = 'CANCELLED'
                await order_shipment.save()
                order.markModified('shipment')
            } else {
                return res.json({
                    success: false,
                    error: cancel_resp.message
                })
            }

            await order.save()
        }
    }

    res.json({
        success: true,
        data: order
    })
}
