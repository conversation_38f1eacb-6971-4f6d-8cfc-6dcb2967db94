const axios = require('axios')
const _ = require('lodash')
const fs = require('fs')
const AWS = require('aws-sdk')
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const { Site, Order, User, Brand, PrintQueue, SiteOrderIndex, Hub, HubStock, HubStockHistory, OrderPayment, OrderShipment, BrandMenu, BrandBillConfig, PrintQueueV2 } = require('../../.shared/database')
const { send_file_to_slack } = require('../../.shared/slack')
const { send_file_to_zalo, send_zalo_message } = require('../../.shared/zalo')
const { upload_file } = require("../../.shared/storage");

const baemin = require('../../.shared/merchant/baemin');
const shopee = require('../../.shared/merchant/shopee');
const shopee_ecom = require('../../.shared/merchant/shopee_ecom');
const gojek = require('../../.shared/merchant/gojek');
const grab = require('../../.shared/merchant/grab');
const be = require('../../.shared/merchant/be');
const lazada = require('../../.shared/merchant/lazada');
const tiktok = require('../../.shared/merchant/tiktok_unofficial');
const haravan = require('../../.shared/merchant/haravan');
const ahamove = require('../../.shared/delivery/ahamove');
const ghn = require('../../.shared/delivery/ghn')
const grab_express = require('../../.shared/delivery/grab_express')
const viettel_post = require('../../.shared/delivery/viettel_post')
const { get_order_list_filter_v2 } = require('../common/report')
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')
const { map_order, map_order_status } = require('../../.shared/merchant/mapping')
const { get_hubs_has_stocks } = require('../common/order')
const token_account = require('../../.shared/token_account')
const momo_mini = require('./momo_mini')
const voucher = require('./voucher')
const { deep_merge_object } = require('../../.shared/helper')
const nexdorpay = require('../../.shared/payment/nexdorpay')
const { get_phone_number_report } = require('../../.shared/merchant/pankcake');
const voucher_dpoint = require('./voucher');
const { get_site_finance_by_source } = require('../../.shared/cron_finance');
const { render_bill_html, render_label_html } = require('../common/bill');
const { send_message_to_topic } = require('../../.shared/event');

/**
 * @param {string[]} permissions
 * @param {string} order_status
 * @param {object} raw_data
 * @returns {{
 *  customer_phone: string,
 *  customer_address: string,
 * }}
 */
const show_customer_info = (permissions, order_status, raw_data) => {
    let customer_address = raw_data?.customer_address ?? '';
    let customer_phone = raw_data?.customer_phone ?? '';

    if (permissions?.includes('view_customer_info_doing_order') && ['WAITING_PAYMENT', 'DOING'].includes(order_status) ||
        permissions?.includes('view_customer_info_finish_order') && ['FINISH', 'CANCEL'].includes(order_status) ||
        permissions?.includes('system')
    ) {
        return {
            customer_phone: customer_phone,
            customer_address: customer_address,
        }
    }
    return {
        customer_phone: '',
        customer_address: '',
    }
}

exports.check_site_accounts = async (req, res) => {
    const site = await Site.findById(req.params.site_id)
    const result = {}
    for (const source of ['gojek', 'grab', 'grab_mart', 'shopee', 'shopee_fresh', 'be']) {
        const token = site.tokens.find(v => v.source === source)
        if (!token || !token.token_code) {
            continue
        }
        const is_working = await token_account.is_token_working(token.token_code)
        if (!is_working) {
            result[source] = false
        }
    }
    res.json({
        success: true,
        data: result,
    })
}

exports.get_phone_validate = async (req, res, next) => {
    if (req.query.phone_number?.length < 10) {
        return res.json({
            success: true,
            data: {
                total_success: 0,
                total_fail: 0,
                warnings: []
            },
        })
    }
    const result = await get_phone_number_report({}, req.query.phone_number)
    res.json({
        success: true,
        data: result
    })
}

exports.get_order_list = async (req, res, next) => {
    const data = await get_database_order_list(req.params.site_id, {
        status: req.query.status,
        from: req.query.from,
        to: req.query.to,
    })
    res.json({
        success: true,
        data: data
    })
}

const filter_by_status = ({ user, permissions, status, from, to }) => {
    let filter = {}
    if (['ALL'].includes(status)) {
        filter.$or = [{
            'data_mapping.order_time_sort': {
                $gte: from ? moment(from).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
                $lte: to ? moment(to).unix() : moment.tz('Asia/Jakarta').unix(),
            }
        }, {
            status: ['DOING', 'PICK'],
            'data_mapping.order_time_sort': {
                $lte: to ? moment(to).unix() : moment.tz('Asia/Jakarta').unix()
            }
        }, {
            status: ['PENDING', 'RETURNING', 'RETURNED'],
            'data_mapping.order_time_sort': {
                $gte: moment.tz('Asia/Jakarta').add(-30, 'days').startOf('day').unix(),
                $lte: moment.tz('Asia/Jakarta').unix(),
            }
        }, {
            user_id: user._id,
        }, {
            status: ['FINISH'],
            'data_mapping.delivery_time_unix': {
                $gte: from ? moment(from).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
                $lte: to ? moment(to).unix() : moment.tz('Asia/Jakarta').unix(),
            }
        }]

        if (permissions.includes('system') || permissions.includes('customer_service')) {
            filter.$or.push({
                status: 'WAITING_PAYMENT'
            })
        }
    } else if (['DOING', 'PICK'].includes(status)) {
        filter['status'] = status
        filter['data_mapping.order_time_sort'] = {}
        filter['data_mapping.order_time_sort'].$lte = to ? moment(to).unix() : moment.tz('Asia/Jakarta').unix()
    } else if (['WAITING_PAYMENT'].includes(status)) {
        if (permissions.includes('system') || permissions.includes('customer_service')) {
            filter = {
                status: 'WAITING_PAYMENT'
            }
        }
    } else if (['PENDING'].includes(status)) {
        filter.$or = [{
            status: ['PENDING'],
            'data_mapping.order_time_sort': {
                $gte: moment.tz('Asia/Jakarta').add(-30, 'days').startOf('day').unix(),
                $lte: moment.tz('Asia/Jakarta').unix(),
            }
        }, {
            user_id: user._id,
        }]
    } else if (['FINISH'].includes(status)) {
        filter['status'] = status
        filter['data_mapping.delivery_time_unix'] = {}
        filter['data_mapping.delivery_time_unix'].$gte = from ? moment(from).unix() : moment.tz('Asia/Jakarta').startOf('day').unix()
        filter['data_mapping.delivery_time_unix'].$lte = to ? moment(to).unix() : moment.tz('Asia/Jakarta').endOf('day').unix()
    } else if (['PRE_ORDER'].includes(status)) {
        filter.$or = [{
            status: 'PRE_ORDER'
        }, {
            status: ['DOING', 'PENDING'], 'data_mapping.order_time_sort': {
                $gte: moment().unix(),
            }
        }]
    } else if (['DRAFT'].includes(status)) {
        filter['status'] = status
        filter['data_mapping.order_time_sort'] = {
            $gte: moment.tz('Asia/Jakarta').startOf('day').unix(), // Only in days
        }
    } else {
        filter['status'] = status
        filter['data_mapping.order_time_sort'] = {}
        filter['data_mapping.order_time_sort'].$gte = from ? moment(from).unix() : moment.tz('Asia/Jakarta').startOf('day').unix()
        filter['data_mapping.order_time_sort'].$lte = to ? moment(to).unix() : moment.tz('Asia/Jakarta').endOf('day').unix()
    }
    return filter
}
exports.get_multi_site_order_list = async (req, res, next) => {
    const { status, from, to, filter_type, hub_ids, brand_ids, site_ids, table, filter_name } = req.query

    let filter = filter_by_status({ user: req.user, permissions: req.permissions, status, from, to })

    const order_filter = await get_order_list_filter_v2(req)
    filter = { ...filter, ...order_filter }

    let sorter = {
        'data_mapping.order_time_sort': 'desc'
    }
    let [page, items_per_page] = [1, 10]

    if (table) {
        page = Number(table.page)
        items_per_page = Number(table.items_per_page)
        if (table.filter) {
            if (table.filter.site_name) {
                const sites = await Site.find({ $text: { $search: table.filter.site_name } }).lean()
                filter.site_id = _.intersection(filter.site_id, sites.map(v => String(v._id)))
            }
            if (table.filter.id) {
                filter['data_mapping.id'] = { $regex: new RegExp(`.*${table.filter.id}.*`, 'i') };
            }

            if (table.filter.order_id) {
                filter['data_mapping.order_id'] = { $regex: new RegExp(`.*${table.filter.order_id}.*`, 'i') };
            }

            if (table.filter.shipment_id) {
                filter['shipment.shipment_id'] = { $regex: new RegExp(`.*${table.filter.shipment_id}.*`, 'i') };
            }

            if (table.filter._vendor_sync !== undefined) {
                filter['vendor_sync.success'] = table.filter._vendor_sync
            }


            if (table.filter.source) {
                filter.source = table.filter.source;
            }
        }
        if (table.sorter) {
            if (table.sorter.column === 'order_id') {
                sorter.order_id = table.sorter.state;
            }
            if (table.sorter.column === 'source') {
                sorter.source = table.sorter.state;
            }
        }
    }

    const order_paginate = await Order.paginate(filter, {
        page: Number(page || 1),
        limit: Number(items_per_page), // Max 100 docs
        sort: { created_at: -1 },
        customLabels: { docs: 'data' },
        lean: true,
        projection: {
            'data_mapping.raw': 0,
        },
    });

    const order_site_ids = _.uniq(order_paginate.data.map(v => v.site_id))
    const order_hub_ids = _.uniq(order_paginate.data.map(v => v.hub_id))
    const order_sites = await Site.find({ _id: order_site_ids }, { _id: 1, name: 1, hub_id: 1 })
    const order_hubs = await Hub.find({ _id: order_hub_ids }, { _id: 1, name: 1 })
    const order_site_map = _.keyBy(order_sites, '_id')
    const order_hub_map = _.keyBy(order_hubs, '_id')
    for (let i = 0; i < order_paginate.data.length; i++) {
        const order_site = order_site_map[order_paginate.data[i].site_id]
        const order_hub = order_hub_map[order_paginate.data[i].hub_id]
        const data_dishes = order_paginate.data[i].data?.dishes ?? []
        const { dishes: raw_dishes, ...data_mapping } = order_paginate.data[i].data_mapping
        const dishes = raw_dishes.map((dish_mapping, index_mapping) => {
            const image = data_dishes.find((_, index_data) => index_mapping === index_data)?.image
            return {
                ...dish_mapping,
                image
            }
        })
        order_paginate.data[i] = {
            ...order_paginate.data[i].data_mapping,
            ..._.pick(order_paginate.data[i], ['site_id', 'shipment', 'vendor_sync', 'status', 'auto_confirmed']),
            site_name: order_site?.name || '',
            hub_name: order_hub?.name || ''
        }

        const { customer_address, customer_phone } = show_customer_info(req.permissions, order_paginate.data[i].status, order_paginate.data[i])
        order_paginate.data[i].customer_address = customer_address
        order_paginate.data[i].customer_phone = customer_phone
    }
    res.json({
        success: true,
        ...order_paginate
    });
}

exports.get_multi_site_order_list_count = async (req, res, next) => {
    const { from, to, filter_type, table, hub_ids, brand_ids, site_ids } = req.query

    const data = {
        ALL: 0,
        DRAFT: 0,
        PRE_ORDER: 0,
        WAITING_PAYMENT: 0,
        PENDING: 0,
        DOING: 0,
        FINISH: 0,
        CANCEL: 0,
        RETURNING: 0,
        RETURNED: 0,
        PICK: 0,
    }

    let filter = await get_order_list_filter_v2(req)

    if (table && table.filter) {
        if (table.filter.site_name) {
            const sites = await Site.find({ $text: { $search: table.filter.site_name } }).lean()
            console.log(sites.map(v => String(v._id)))
            filter.site_id = _.intersection(filter.site_id, sites.map(v => String(v._id)))
        }
        if (table.filter.order_id) {
            filter['data_mapping.order_id'] = { $regex: `.*${table.filter.order_id}.*` };
        }
        if (table.filter.source) {
            filter.source = table.filter.source;
        }
    }

    await Promise.all(Object.keys(data).map(async (status) => {
        let filter_with_status = {
            ...filter,
            ...filter_by_status({
                user: req.user, permissions: req.permissions,
                status, from, to
            })
        }

        const order_count = await Order.count(filter_with_status)
        data[status] += order_count
    }));

    res.json({
        success: true,
        data: data
    })
}

exports.get_multi_site_new_orders = async (req, res, next) => {
    const { filter_type, hub_ids, brand_ids, site_ids, table } = req.query

    let filter = {
        source: { $ne: 'local' },
        status: ['DOING', 'PENDING'],

    }
    filter['data_mapping.order_time_sort'] = {}
    filter['data_mapping.order_time_sort'].$gte = moment.tz('Asia/Jakarta').add(-3, 'minute').unix()

    if (filter_type == 'hub') {
        const sites = await Site.find({ hub_id: hub_ids })
        filter.site_id = sites.map(v => String(v._id))
    }
    if (filter_type == 'brand') {
        const sites = await Site.find({ brand_id: brand_ids })
        filter.site_id = sites.map(v => String(v._id))
    }
    if (filter_type == 'site') filter.site_id = site_ids

    const order = await Order.findOne(filter).sort({ "data_mapping.order_time_sort": - 1 }).lean()
    if (order) {
        res.json({
            success: true,
            data: {
                has_new_order: true,
                last_order_time: order.data_mapping.order_time_sort,
                order: order.data_mapping,
            }
        });
        return
    }
    res.json({
        success: true,
        data: {
            has_new_order: false,
        }
    })
}

const get_database_order_list = async (site_id, { source, status, from, to, not_ready }) => {
    let filter = {
        status: status,
        site_id: site_id,
        'data_mapping.order_time_sort': {
            $gte: from ? moment(from).unix() : moment.tz('Asia/Jakarta').startOf('day').unix(),
            $lt: to ? moment(to).unix() : moment.tz('Asia/Jakarta').endOf('day').unix(),
        },
    }
    if (source)
        filter.source = source

    if (not_ready)
        filter["data_mapping.ready_time"] = null


    const orders = await Order.find(filter, { data_mapping: 1 }).sort({ "data_mapping.order_time_sort": -1 }).lean()

    const data = orders.map(v => v.data_mapping)
    return data
}

exports.get_order_detail = async (req, res) => {
    const order = await Order.findOne({ order_id: req.params.order_id }).lean()

    const print = await PrintQueueV2.findOne({ order_id: req.params.order_id }).lean()
    const user = await User.findById(order.user_id).lean()
    const site = await Site.findById(order.site_id)
    const hub = await Hub.findById(order.hub_id, { _id: 1, name: 1, address: 1 }).lean()

    let rejected_hubs = []
    if (order.rejected_hubs) {
        const hub_ids = order.rejected_hubs.map(v => v.hub_id)
        const hubs = await Hub.find({ _id: hub_ids }, { name: 1 }).lean()
        rejected_hubs = order.rejected_hubs.map(v => {
            const hub = hubs.find(h => String(h._id) === String(v.hub_id))
            return {
                ...v,
                hub_name: hub?.name || ""
            }
        })
    }

    const transactions = await OrderPayment.find({ order_id: order.order_id }).lean()


    const order_detail = {
        ...order?.data_mapping,
        raw: order?.data,
        status: order.status,
        created_at: order?.created_at,
        printed_at: print ? print.created_at : null,
        creator_name: user?.name || "",
        creator_email: user?.email || "",
        vendor_sync: order?.vendor_sync,
        he_id: order?.he_id,
        site: _.pick(site, '_id', 'name', 'address'),
        hub,
        transactions,
        rejected_hubs,
        shipment: order?.shipment,
    }

    const order_shipments = await OrderShipment.find({ order_id: order.order_id }).sort({ created_at: -1 }).lean()
    order_detail.order_shipments = order_shipments

    const { customer_address, customer_phone } = show_customer_info(req.permissions, order.status, order_detail)

    order_detail.customer_address = customer_address
    order_detail.customer_phone = customer_phone

    return res.json({
        success: true,
        data: order_detail,
    })
}

exports.get_order_detail_latest = async (req, res) => {
    let db_order = await Order.findOne({ order_id: req.params.order_id }).lean()

    if (['he', 'local'].includes(db_order.source)) {
        db_order.data_mapping = map_order(db_order.source, db_order.data)
        await Order.findOneAndUpdate({ order_id: req.params.order_id }, { data_mapping: db_order.data_mapping }).lean()
        return this.get_order_detail(req, res)
    }
    const app_functions = {
        'shopee': shopee,
        'shopee_fresh': shopee,
        'gojek': gojek,
        'grab': grab,
        'grab_mart': grab,
        'be': be,
        'shopee_ecom': shopee_ecom,
        'lazada': lazada,
        'tiktok': tiktok,
    }
    const merchantFunc = app_functions[db_order.source]
    if (!merchantFunc) {
        return res.json({
            success: false,
            error: 'source_not_support',
        })
    }
    const site = await Site.findById(db_order.site_id)
    const token = await token_account.get_token_by_site(site, db_order.source)

    let merchant_order = await merchantFunc.get_order_detail(token, db_order.order_id)
    merchant_order = deep_merge_object(db_order?.data || {}, merchant_order)
    const data_mapping = map_order(db_order.source, merchant_order)
    const update = { data: merchant_order, data_mapping }
    const new_status = map_order_status(db_order.source, merchant_order)
    if (new_status) {
        update.status = new_status
    }
    await Order.updateOne({ order_id: req.params.order_id }, update)
    await get_site_finance_by_source(db_order.order_id)
    await this.get_order_detail(req, res)
}

exports.update_order = async (req, res) => {
    const { order_id } = req.params;
    const { status } = req.body;
    const updateFields = {};

    const order = await Order.findOne({ order_id }).lean();
    if (!order) {
        return res.json({
            success: false,
            error: 'order_not_found',
        });
    }

    if (status) {
        updateFields.status = status;
    }

    if (['PICK', 'FINISH', 'CANCEL'].includes(order.status) && status === 'CANCEL' && !req.permissions.includes('system') && !req.permissions.includes('customer_service')) {
        return res.json({
            success: false,
            error: 'order_not_cancel',
        });
    }

    if (status === 'DOING') {
        updateFields['data_mapping.pick_time'] = moment().toISOString();
    }

    if (status === 'FINISH') {
        const is_first_order = order.user_id ? await Order.count({ user_id: order.user_id, status: 'FINISH' }) === 0 : undefined;

        const now = moment(order.created_at)
        updateFields['data.pick_time'] = now.toISOString();
        updateFields['data_mapping.pick_time'] = now.toISOString();
        updateFields['data.delivery_time'] = now.toISOString();
        updateFields['data_mapping.delivery_time'] = now.toISOString();
        updateFields['data.delivery_time_unix'] = now.unix();
        updateFields['data_mapping.delivery_time_unix'] = now.unix();
        updateFields['is_first_order'] = is_first_order;

        let phone = order.data_mapping.customer_phone;
        if (order.user_id) {
            const user = await User.findById(order.user_id).lean();
            phone = user?.phone || phone;
        }
        const { code, discount } = order.voucher || {};
        const result = await voucher.send_completed_order({
            order_id: order.order_id,
            external_id: order.external_id,
            phone_number: phone,
            total: order.data_mapping.total_for_biz,
            discount: discount,
            delivery_on: updateFields['data_mapping.delivery_time'],
            voucher_code: code,
        }, order.site_id);
        updateFields.dpoint_sync = result;
    }

    if (['DOING', 'FINISH', 'PICK', "CANCEL"].includes(status) && order.source === 'momo') {
        const error = await momo_mini.update_order_status({
            order_id: order.order_id,
            order_status: status,
            driver_name: order.data_mapping.driver_name,
            driver_phone: order.data_mapping.driver_phone,
            site_id: order.site_id,
        });

        if (error) {
            return res.json({
                success: false,
                error: error,
            });
        }
    }

    await Order.updateOne({ order_id }, { $set: updateFields });

    res.json({
        success: true,
    });
};


exports.delete_order = async (req, res) => {
    const { order_id } = req.params;

    const order = await Order.findOne({ order_id }).lean();
    if (!order) {
        return res.json({
            success: false,
            error: 'order_not_found',
        });
    }

    if (order.status !== 'CANCEL') {
        return res.json({
            success: false,
            error: 'order_not_cancel',
        });
    }

    await Order.deleteOne({ order_id });
    res.json({
        success: true,
    });
};


exports.get_site_has_stock = async (req, res) => {
    const { order_id } = req.params;
    const order = await Order.findOne({ order_id }).lean()
    const site = await Site.findById(order.site_id).lean()
    const sites = await Site.find({ brand_id: site.brand_id, active: true }).lean()
    // TODO: limit site by stocks
    res.json({
        success: true,
        data: sites.map(v => _.pick(v, ['_id', 'name', 'code', 'active']))
    });
};

exports.put_site_has_stock = async (req, res) => {
    const { order_id, site_id } = req.params;
    const order = await Order.findOne({ order_id }).lean()
    const site = await Site.findById(order.site_id).lean()
    const sites = await Site.find({ brand_id: site.brand_id, active: true }).lean()

    if (!sites.map(v => String(v._id)).includes(site_id)) {
        return res.json({
            success: false,
            error_code: "site_not_belong_to_brand",
            message: "Site is not belong to brand"
        });
    }
    await Order.updateOne({ order_id }, { $set: { site_id } });
    res.json({
        success: true,
    });
};


exports.get_hub_has_stock = async (req, res) => {
    const { order_id } = req.params;
    const order = await Order.findOne({ order_id }).lean()
    const site = await Site.findById(order.site_id).lean()
    const hubs = await Hub.find({ _id: { $in: [site.hub_id, ...(site.hub_ids || [])], $nin: order.hub_id } }, { _id: 1, name: 1, address: 1 }).lean()
    res.json({
        success: true,
        data: hubs
    });
};

exports.put_hub_has_stock = async (req, res) => {
    const { order_id, hub_id } = req.params;
    const order = await Order.findOne({ order_id })
    if (!['he', 'local', 'momo'].includes(order.source)) {
        return res.json({
            success: false,
            error: 'order_not_support_change_hub',
        });
    }
    const site = await Site.findById(order.site_id)
    const brand = await Brand.findById(site.brand_id)
    const old_hub = await Hub.findById(order.hub_id)
    const new_hub = await Hub.findById(hub_id)
    if (order.shipment?.shipment_id && order.shipment?.status !== 'CANCELLED') {
        const merchant_functions = {
            'ahamove': ahamove.cancel_order,
            'grab_express': grab_express.cancel_order,
            'grab_express_2h': grab_express.cancel_order,
            'viettel_post': viettel_post.cancel_order,
        }
        const order_shipment = await OrderShipment.findOne({ shipment_id: order.shipment.shipment_id })
        const merchant_function = merchant_functions[order.shipment?.vendor]
        if (merchant_function) {
            const token = await token_account.get_token_by_site(site, order.shipment.vendor)
            const cancel_resp = await merchant_function(token, {
                shipment_id: order.shipment.shipment_id,
                order_id: order.order_id,
            })
            if (cancel_resp.success) {
                order_shipment.status = order.shipment.status = 'CANCELLED'
                await order_shipment.save()
            } else {
                return res.json({
                    success: false,
                    error: cancel_resp.message
                })
            }
        }
    }
    order.shipment.from = {
        name: new_hub.name,
        address: new_hub.address,
        phone: new_hub.phone,
    }
    order.hub_id = new_hub._id
    order.markModified('shipment')
    await order.save()
    await send_zalo_message_by_order_id({
        order_id: order.order_id,
        message: [`Bạn được <b>${old_hub.name}</b> chuyển cho đơn hàng <b>${order_id}</b>,`, `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`].join('\n'),
    })
    res.json({
        success: true,
    });
};

exports.confirm_payment = async (req, res) => {
    const data = _confirm_payment({
        order_id: req.params.order_id,
        note: req.body.note || '',
        image_url: req.body.image_url || '',
        total: req.body.total,
        payment_method: req.body.payment_method,
        index: req.body.index,
    })

    res.json({
        success: true,
        data: data
    })
}

exports.delete_payment = async (req, res) => {
    const { order_id } = req.params
    const db_order = await Order.findOne({ order_id })

    const index = Number(req.body.index ?? 0)
    db_order.data.payments.splice(index, 1);

    db_order.data_mapping.payments = db_order.data.payments
    db_order.markModified('data')
    db_order.markModified('data_mapping')

    await db_order.save()
    res.json({
        success: true,
    })
}


exports.change_payment_method = async (req, res) => {
    const { order_id } = req.params;
    const { payment_method } = req.body;

    const order = await Order.findOne({ order_id })
    if (!order) {
        return res.json({
            success: false,
            error: 'order_not_found',
        });
    }
    if (order.status === 'FINISH') {
        return res.json({
            success: false,
            error: 'order_finished',
        });
    }

    const pending_payments = order.data_mapping.payments.filter(v => v.status === 'PENDING');
    if (pending_payments.length === 0) {
        return res.json({
            success: false,
            error: 'no_pending_payment',
        });
    }

    // update payment method
    pending_payments.forEach(v => v.method = payment_method);
    order.data.payments = order.data_mapping.payments;

    order.markModified('data_mapping');
    order.markModified('data');
    await order.save();

    res.json({
        success: true,
    });
}

exports.confirm_order = async (req, res) => {
    const { option, pickup_time, use_shipment } = req.body
    const data = await _confirm_order(req.params.order_id, { option, pickup_time, use_shipment })

    res.json({
        success: true,
        data: data
    })
}

exports.cancel_order = async (req, res) => {
    const { cancel_type, cancel_reason } = req.body
    const data = await _cancel_order(req.params.order_id, {
        cancel_by: String(req.user?._id),
        cancel_type,
        cancel_reason,
    })

    return res.json({
        success: data.success,
        error: data.message,
    })
}

exports.auto_confirm_order = async (req, res) => {
    const orders = await Order.find({
        status: ['PENDING', 'DOING'],
        source: ['grab', 'grab_mart', 'shopee', 'shopee_fresh', 'be'],
        $or: [
            {
                source: { $nin: ['grab', 'grab_mart'] },
                'data_mapping.order_time_sort': {
                    $gte: moment.tz('Asia/Jakarta').startOf('day').unix()
                }
            },
            {
                source: { $in: ['grab', 'grab_mart'] },
                'data_mapping.order_time_sort': {
                    $gte: moment.tz('Asia/Jakarta').startOf('day').unix(),
                    $lte: moment().subtract(15, 'minutes').unix()
                }
            }
        ],
        site_id: { $nin: ['6729865aeae6aa77434b24ed'] },
        auto_confirmed: false
    }).limit(10);

    const result = []
    for (const order of orders) {
        const data = await _confirm_order(order.order_id);
        result.push(data);
    }

    res.json({
        success: true,
        data: result
    })
}

const _confirm_order = async (order_id, confirm_data) => {
    const db_order = await Order.findOne({ order_id })

    const site = await Site.findById(db_order.site_id)
    const hub = await Hub.findById(db_order.hub_id)
    let data = {}

    if (process.env.USE_MERCHANT_APPS === "true") {
        const app_functions = {
            'shopee': shopee,
            'shopee_fresh': shopee,
            'gojek': gojek,
            'grab': grab,
            'grab_mart': grab,
            'be': be,
            'shopee_ecom': shopee_ecom,
            'lazada': lazada,
            'tiktok': tiktok,
        }
        const merchantFunc = app_functions[db_order.source]
        if (merchantFunc) {
            const token = await token_account.get_token_by_site(site, db_order.source)
            data = await merchantFunc.confirm_order(token, order_id)
            db_order.auto_confirmed = true
        }
    }


    if (db_order.source === 'local') {
        db_order.status = 'DOING'
    }

    if (db_order.source === 'he' || db_order.source === 'momo') {
        if (db_order.shipment) {
            db_order.shipment.from = {
                address: hub.address,
                phone: hub.phone,
                name: hub.name,
            }
        }
        db_order.status = 'DOING'

        const total_paid = _.sumBy(db_order.data_mapping.payments?.filter(v =>
            v.status === 'COMPLETED'
            && v.method !== 'CASH'
            && v.method !== 'COD'
        ) || [], 'total') || 0

        let total_remain = db_order.data_mapping.total_for_biz - total_paid
        if (total_remain < 0) { total_remain = 0 }

        let schedule_order_time = null
        if (db_order.shipment?.schedule?.from_date_time) {
            schedule_order_time = moment(db_order.shipment.schedule.from_date_time).unix()
            if (schedule_order_time < moment().unix()) {
                schedule_order_time = null
            }
        }
        if (confirm_data?.pickup_time) {
            schedule_order_time = moment(confirm_data?.pickup_time).unix()
        }
        if (schedule_order_time === null) {
            schedule_order_time = moment().add(1, 'minute').unix()
        }

        if (db_order.source === 'momo') {
            const error = await momo_mini.update_order_status({
                order_id: db_order.order_id,
                order_status: 'DOING',
                site_id: db_order.site_id,
            });

            if (error) {
                throw new Error(error);
            }
        }
        // create shipment order
        if (confirm_data?.use_shipment && db_order.shipment) {
            const ship_services = {
                'ahamove': ahamove.create_order,
                'grab_express': grab_express.create_order,
                'grab_express_2h': grab_express.create_order,
                'viettel_post': viettel_post.create_order,
            }
            if (confirm_data?.option) {
                db_order.shipment.service.option = confirm_data?.option
            }

            const ship_service = ship_services[db_order.shipment.vendor]

            if (ship_service) {
                const token = await token_account.get_token_by_site(site, db_order.shipment.vendor)
                const ship = await ship_service(token, {
                    promo_code: db_order.shipment.promo_code,
                    dishes: db_order.data_mapping.dishes,
                    from: db_order.shipment.from,
                    to: db_order.shipment.to,
                    service: db_order.shipment.service,
                    cod: total_remain,
                    note: '',
                    tracking_number: db_order.order_id,
                    schedule_order_time,
                })
                db_order.shipment.shipment_id = ship.shipment_id
                db_order.shipment.tracking_url = ship.tracking_url
                db_order.shipment.price = ship.price
                db_order.shipment.status = 'ORDER_CREATED'
                await OrderShipment.create({
                    is_sub_shipment: false,
                    cod: total_remain,
                    order_id: db_order.order_id,
                    vendor: db_order.shipment.vendor,
                    shipment_id: ship.shipment_id,
                    from_name: db_order.shipment.from?.name,
                    from_phone: db_order.shipment.from?.phone,
                    from_address: db_order.shipment.from?.address,
                    to_name: db_order.shipment.to?.name,
                    to_phone: db_order.shipment.to?.phone,
                    to_address: db_order.shipment.to?.address,
                    tracking_url: ship.tracking_url,
                    price_for_user: db_order.shipment?.price ?? ship.price,
                    price: ship.price,
                    status: 'ORDER_CREATED',
                    webhooks: [ship],
                })
            }
            db_order.markModified('shipment')
        }
    }

    await db_order.save()

    return data
}

const _cancel_order = async (order_id, { cancel_by, cancel_type, cancel_reason }) => {
    const db_order = await Order.findOne({ order_id })
    const site = await Site.findById(db_order.site_id)
    const brand = await Brand.findById(site.brand_id)
    const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id })
    let result = {
        success: true,
        message: "success"
    }

    if (process.env.USE_MERCHANT_APPS === "true") {
        const app_functions = {
            'shopee': shopee,
            'shopee_fresh': shopee,
            'gojek': gojek,
            'grab': grab,
            'grab_mart': grab,
            'be': be,
        }
        if (app_functions[db_order.source]) {
            const token = await token_account.get_token_by_site(site, db_order.source)
            const merchantFunc = app_functions[db_order.source]
            const cancel_resp = await merchantFunc.cancel_order(token, order_id, { cancel_type, cancel_reason })
            result = cancel_resp
        }
    }

    // if (db_order.source === 'local') {
    //     db_order.status = 'CANCEL'
    //     const all_stocks = db_order.data_mapping.dishes?.flatMap(dish => dish.stocks).flatMap(v => v).filter(Boolean) || [];
    //     const site = await Site.findById(db_order.site_id);
    //     const order_hub = await Hub.findById(db_order.hub_id);
    //     for (const stock of all_stocks) {
    //         const hub_stock = await HubStock.findOne({ hub_id: site.hub_id, code: stock.code });
    //         if (!hub_stock) {
    //             continue;
    //         }
    //         await HubStockHistory.create({
    //             hub_id: site.hub_id,
    //             code: hub_stock.code,
    //             from_quantity: hub_stock.quantity,
    //             to_quantity: hub_stock.quantity + stock.quantity,
    //             updated_type: 'order_cancel',
    //             updated_order_id: db_order.order_id,
    //         })
    //         hub_stock.quantity += stock.quantity;
    //         await hub_stock.save();
    //     }
    //     // Send message to zalo group in site or hub configured
    //     await send_zalo_message_by_order_id({
    //         order_id,
    //         message: [
    //             `Bạn đã hủy đơn hàng ${order_id} tại hub ${order_hub.name},`,
    //             `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order_id}`
    //         ].join('\n'),
    //     })

    // }

    if (db_order.source === 'he' || db_order.source === 'momo' || db_order.source === 'local') {
        if (['out_stock', 'merchant_busy'].includes(cancel_type)) {
            db_order.rejected_hubs = [
                ...(db_order.rejected_hubs || []),
                {
                    hub_id: String(db_order.hub_id),
                    time: moment().toISOString(),
                    user_id: cancel_by,
                    type: cancel_type,
                    reason: cancel_reason,
                },
            ]
            if (site.hub_id === db_order.hub_id) {
                // return res.status(400).json({
                //     success: false,
                //     error: 'hub_cs_can_not_reject_order',
                // })

                throw new Error('hub_cs_can_not_reject_order')
            }
            // only reassign if less than 3 hubs rejected
            let hub_with_stocks = []

            if (db_order.rejected_hubs.length < 3) {
                hub_with_stocks = await get_hubs_has_stocks({
                    categories: brand_menu.categories,
                    items: db_order.data_mapping.dishes,
                    address: db_order.data_mapping.customer_address,
                    site,
                }, site.hub_ids?.filter(v => !db_order.rejected_hubs?.map(v => v.hub_id).includes(v)))
            }

            const old_hub = await Hub.findById(db_order.hub_id)
            const new_hub = hub_with_stocks?.find(hub => hub.has_stock)
            db_order.hub_id = new_hub?._id || site.hub_id
            await db_order.save()

            if (db_order.hub_id !== String(old_hub._id)) {
                // Send message to zalo group in site or hub configured
                await send_zalo_message_by_order_id({
                    order_id: db_order.order_id,
                    message: [
                        `Bạn nhận được đơn hàng ${order_id} từ hub ${old_hub.name},`,
                        `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order_id}`,
                    ].join('\n')
                })
            }

            if (db_order.shipment?.shipment_id) {
                const merchant_functions = {
                    'ahamove': ahamove.cancel_order,
                    'grab_express': grab_express.cancel_order,
                    'grab_express_2h': grab_express.cancel_order,
                    'viettel_post': viettel_post.cancel_order,
                }
                const order_shipment = await OrderShipment.findOne({ shipment_id: db_order.shipment.shipment_id })
                const merchant_function = merchant_functions[db_order.shipment?.vendor]
                if (merchant_function) {
                    const token = await token_account.get_token_by_site(site, db_order.shipment.vendor)
                    const cancel_resp = await merchant_function(token, {
                        shipment_id: db_order.shipment.shipment_id,
                        order_id: db_order.order_id,
                    })
                    if (cancel_resp.success) {
                        order_shipment.status = db_order.shipment.status = 'CANCELLED'
                        await order_shipment.save()
                    }
                }
            }
            db_order.shipment.from = {
                address: new_hub.address,
                phone: new_hub.phone,
                name: new_hub.name,
            }
            db_order.markModified('shipment')
        } else {
            db_order.status = 'CANCEL'
        }
    }

    if (db_order.source === 'momo') {
        // update only then order is cancelled
        if (db_order.status === 'CANCEL') {
            await momo_mini.update_order_status({
                order_id: db_order.order_id,
                order_status: 'CANCEL',
                site_id: db_order.site_id,
            })
        }
    }

    if (db_order.source === 'he' || db_order.source === 'local') {
        // update only then order is cancelled
        if (db_order.status === 'CANCEL') {
            const dpoint_voucher = db_order.data_mapping.coupons?.find(v => v.name === 'Voucher Dpoint')
            if (dpoint_voucher) {
                await voucher_dpoint.release_voucher(dpoint_voucher.code, site)
            }
        }
    }
    await db_order.save()

    return result

}


const _confirm_payment = async ({ order_id, note, image_url, total, payment_method, index = 0 }) => {
    const db_order = await Order.findOne({ order_id })
    const site = await Site.findById(db_order.site_id)
    const brand = await Brand.findById(site.brand_id)
    const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }, { categories: 1 }).lean()
    const cod_token = brand.getToken('cod')
    const max_cod_setting = Number(cod_token?.site_data?.max_cod || 1000000)

    const payment_item = db_order.data.payments[index]
    if (payment_item.method === 'COD' && payment_item.total > max_cod_setting && total > 0) {
        payment_item.total -= total
        payment_item.status = 'COMPLETED'
        db_order.data.payments.push({
            method: payment_method || 'PREPAID',
            total: total,
            status: 'COMPLETED',
            note: note,
        })
    } else {
        payment_item.status = 'COMPLETED'
        payment_item.note = note
        payment_item.image_url = image_url
    }

    if (db_order.hub_id === site.hub_id && site.hub_ids?.length > 0) {
        const hub_with_stocks = await get_hubs_has_stocks(
            {
                categories: brand_menu.categories,
                items: db_order.data_mapping.dishes,
                address: db_order.data_mapping.customer_address,
                site,
            },
            site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id]
        )

        const hub_to_assign = hub_with_stocks?.find((v) => v.has_stock)
        if (hub_to_assign) {
            db_order.hub_id = hub_to_assign._id
        }
    }


    db_order.data_mapping.payments = db_order.data.payments
    db_order.markModified('data')
    db_order.markModified('data_mapping')

    if (db_order.status === 'WAITING_PAYMENT') {
        const auto_confirm_order = !site.hub_ids?.length && db_order.source === 'local'
        db_order.status = auto_confirm_order ? 'DOING' : 'PENDING'
        // await send_zalo_message_by_order_id({
        //     order_id: db_order.order_id,
        //     message: [
        //         `Bạn có đơn hàng mới <b>${db_order.order_id}</b>,`,
        //         `Khách hàng: ${db_order.data_mapping.customer_name}, ${db_order.data_mapping.customer_address},`,
        //         `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${db_order.order_id}`
        //     ].join('\n'),
        // }).catch(console.log)
    }

    await db_order.save()
}

exports.auto_print_order_bill = async (req, res) => {
    const sites = await Site.find({
        active: true,
        auto_print: true,
    }, { _id: 1, auto_print: 1 })
    const result = []

    const orders = await Order.find({
        // source: { $nin: ['grab', 'grab_mart', 'shopee', 'shopee_fresh', 'be'] },
        status: ['PENDING', 'DOING', 'PICK', 'FINISH'],
        site_id: sites.map(v => v._id),
        auto_printed: false,
        'data_mapping.order_time_sort': {
            $gte: moment.tz('Asia/Jakarta').add(-2, 'day').unix(),
        },
    }).limit(2);


    if (orders.length > 0) {
        await Order.updateMany({ order_id: orders.map(v => v.order_id) }, { auto_printed: true })
    }
    for (const order of orders) {
        await this.send_order_bill_to_channel(order.order_id, {
            zalo: true,
            zalo_message: [
                `<bc style="color:#28a745">BẠN CÓ ĐƠN HÀNG MỚI: ${order.data_mapping.order_id}</bc>,`,
                ['local', 'he'].includes(order.source) ?
                    `Khách hàng: ${order.data_mapping.customer_name}, ${order.data_mapping.customer_address}` :
                    `Khách hàng: ${order.data_mapping.customer_name}`,
                `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
            ].join('\n'),
            bill: true,
            bill_template: 'bill_for_kitchen',
            labels: true
        })

        await this.send_order_bill_to_channel(order.order_id, {
            zalo: false,
            bill: true,
            bill_template: ['grab', 'grab_mart', 'shopee', 'shopee_fresh', 'be'].includes(order.source) ? 'bill_for_complete_app' : 'bill_for_complete',
            labels: false
        })
    }

    const cancel_orders = await Order.find({
        status: ['CANCEL'],
        site_id: sites.map(v => v._id),
        auto_cancel_printed: false,
        'data_mapping.order_time_sort': {
            $gte: moment.tz('Asia/Jakarta').add(-2, 'day').unix(),
        },
    });

    if (cancel_orders.length > 0) {
        await Order.updateMany({ order_id: cancel_orders.map(v => v.order_id) }, { auto_cancel_printed: true })
    }
    for (const order of cancel_orders) {
        await this.send_order_bill_to_channel(order.order_id, {
            zalo: true,
            zalo_message: [
                `<bc style="color:#ff0000">ĐƠN HÀNG ĐÃ BỊ HỦY: ${order.data_mapping.order_id}</bc>,`,
                `Lý do: ${order.data_mapping.cancel_reason}`,
                `Xem chi tiết tại: ${process.env.WEB_URL}/?orderId=${order.order_id}`
            ].join('\n'),
            urgent_message: true,
            bill: true,
            bill_template: 'bill_for_cancel',
            labels: false
        })
    }

    res.json({
        success: true,
        data: result
    })
}

exports.auto_print_order_label = async (req, res) => {
    const sites = await Site.find({
        active: true,
        auto_print_label: true,
    }, { _id: 1, auto_print_label: 1 })

    const result = []

    const orders = await Order.find({
        status: ['DOING', 'PICK', 'FINISH'],
        site_id: sites.map(v => v._id),
        auto_label_printed: false,
        'data_mapping.order_time_sort': {
            $gte: moment.tz('Asia/Jakarta').add(-2, 'day').unix(),
        },
    });

    await Order.updateMany({ order_id: orders.map(v => v.order_id) }, { auto_label_printed: true })
    for (const order of orders) {
        await this.send_order_bill_to_channel(order.order_id, { zalo: false, bill: false, labels: true })
    }
    res.json({
        success: true,
        data: result
    })
}


exports.auto_generate_bill = async (req, res) => {
    const sites = await Site.find({
        active: true,
        auto_print: true,
    }, { _id: 1, auto_print: 1, auto_print_label: 1 })

    const orders = await Order.find({
        site_id: sites.map(v => v._id),
        sent_bill_notification: false,
        bill_url: { $exists: false },
        'data_mapping.order_time_sort': {
            $gte: moment().add(-2, 'day').unix(),
        },
    }).select(['order_id', 'site_id', 'hub_id']).limit(3);
    await Order.updateMany({ order_id: orders.map(v => v.order_id) }, { sent_bill_notification: true })

    for (const order of orders) {
        if (!order.bill_url) {
            await this.generate_order_bills(order.order_id, { generate_bill: true })
        }
        if (order.label_urls?.length === 0) {
            await this.generate_order_bills(order.order_id, { generate_label: true })
        }
    }

    res.json({
        success: true,
        data: orders.map(v => v.order_id)
    })
}

const get_bill_by_template = (name) => {
    if (!['bill_for_kitchen', 'bill_for_payment', 'bill_for_complete', 'bill_for_complete_app', 'label'].includes(name)) {
        return null
    }
    return fs.readFileSync('files/' + name + '.ejs', 'utf8')
}

// TODO: move all _print_order to v2
exports.print_order_v2 = async (order, { template = 'bill_for_kitchen', use_printer = true, send_notification = true, qrcode = null, auto_print = false }) => {
    const bill_html_preview = await render_bill_html({ bill_type: template, order_id: order.order_id });
    if (!bill_html_preview) {
        return null
    }
    const bill_resp = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://tools.nexpos.io/convert',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
            order_code: order.order_id,
            html_string: bill_html_preview
        }
    })
    return {
        bill: bill_resp.data,
        labels: [],
    }
}

const map_bill_to_order_field = {
    'bill_for_kitchen': 'bill_url',
    'bill_for_payment': 'bill_for_payment_url',
    'bill_for_complete': 'bill_for_complete_url',
    'bill_for_complete_app': 'bill_for_complete_app_url',
    'bill_for_cancel': 'bill_for_cancel_url',
}

exports.generate_order_bills = async (order_id, { bill_type = 'bill_for_kitchen', generate_bill = false, generate_label = false }) => {
    const order = await Order.findOne({ order_id });

    if (generate_bill) {
        const bill_html_preview = await render_bill_html({ bill_type, order_id: order.order_id });
        if (bill_html_preview) {
            const bill_resp = await axios({
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://tools.nexpos.io/convert',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data: {
                    order_code: order.order_id,
                    html_string: bill_html_preview
                }
            })
            order[map_bill_to_order_field[bill_type] || 'bill_url'] = bill_resp.data;
        }
    }

    if (generate_label) {
        const label_urls = []
        for (let dish_index = 0; dish_index < order.data_mapping.dishes.length; dish_index++) {
            for (let label_index = 0; label_index < order.data_mapping.dishes[dish_index].quantity; label_index++) {
                const label_html_preview = await render_label_html({ order_id: order.order_id, dish_index, label_index, size: '500px' });
                if (label_html_preview) {
                    const label_resp = await axios({
                        method: 'post',
                        maxBodyLength: Infinity,
                        url: 'https://tools.nexpos.io/convert',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        data: {
                            order_code: order.order_id,
                            html_string: label_html_preview
                        }
                    })
                    label_urls.push(label_resp.data)

                }
            }
        }
        order.label_urls = label_urls
    }
    await order.save();
    return order;
}

exports.send_order_bill_to_channel = async (order_id, { bill_template, label_template = 'bill_for_label', zalo_message, zalo = true, urgent_message = false, bill = true, labels = true }) => {
    const order = await Order.findOne({ order_id });
    const site = await Site.findById(order.site_id);
    const hub = await Hub.findById(order.hub_id);
    const zalo_group = site.zalo_group || hub.zalo_group

    let current_bill_url = null
    let label_urls = order.label_urls || []
    if (bill) {
        const field = map_bill_to_order_field[bill_template]
        if (field) {
            if (!order[field]) {
                const new_order = await this.generate_order_bills(order_id, { bill_type: bill_template, generate_bill: true })
                current_bill_url = new_order[field]
            } else {
                current_bill_url = order[field]
            }
        }
    }
    if (label_urls.length === 0) {
        const new_order = await this.generate_order_bills(order_id, { generate_label: true })
        label_urls = new_order.label_urls || []
    }


    if (zalo && zalo_group?.startsWith('https://zalo.me/g/')) {
        if (zalo_message) {
            await send_zalo_message({
                message: zalo_message,
                group_link: zalo_group,
                urgency: urgent_message ? 2 : ''
            })
        }
        if (current_bill_url) {
            await send_file_to_zalo({ url: current_bill_url, group_link: zalo_group })
        }
    }


    await send_message_to_topic({
        topic: 'print_order',
        message: `Order ${order.order_id} - ${hub.name} is ready to print`,
        data: {
            bill_template: bill ? bill_template : null,
            label_template: labels ? label_template : null,
            hub_code: hub.code,
            order_id: order.order_id,
            site_id: order.site_id,
            bill_url: bill ? current_bill_url : null,
            label_urls: labels ? label_urls : [],
        }
    })

    const message_data = []
    if (bill && current_bill_url) {
        message_data.push({
            template: bill_template,
            hub_code: hub.code,
            order_id: order.order_id,
            site_id: order.site_id,
            urls: [current_bill_url],
        })
    }
    if (labels && label_urls.length > 0) {
        message_data.push({
            template: label_template,
            hub_code: hub.code,
            order_id: order.order_id,
            site_id: order.site_id,
            urls: label_urls,
        })
    }
    if (message_data.length > 0) {
        await send_message_to_topic({
            topic: 'print_bill',
            message: `Order ${order.order_id} - ${hub.name} is ready to print`,
            data: {
                messages: message_data,
            }
        })
    }

    // Old modal support
    if (bill && current_bill_url) {
        await PrintQueueV2.create({
            site_id: order.site_id,
            hub_id: order.hub_id,
            order_id: order.order_id,
            status: 'created',
            file_url: current_bill_url,
        })
    }
}

const _print_order = async (order, {
    template = 'bill_for_kitchen',
    use_printer = true, send_notification = true,
    qrcode = null,
    auto_print = false,
}) => {
    const site = await Site.findById(order.site_id)
    const hub = await Hub.findById(order.hub_id)

    const printed_order = await PrintQueue.findOne({ site_id: order.site_id, hub_id: order.hub_id, order_id: order.order_id }).sort({ created_at: -1 })

    let order_count_message = ""
    const order_group = `${site.code?.toUpperCase() || "NEXDOR"}-${moment().format('YYMMDD')}`
    if (printed_order) {
        const site_next_index = await SiteOrderIndex.findOneAndUpdate(
            { site_id: site._id, group: order_group },
            {},
            { new: true, upsert: true }
        )
        order_count_message = `Đơn hàng thứ: ${site_next_index.current_index.toString()} trong ngày (In lại lần  ${printed_order.order_print_count})`
    } else {
        const site_next_index = await SiteOrderIndex.findOneAndUpdate(
            { site_id: site._id, group: order_group },
            { $inc: { current_index: 1 } },
            { new: true, upsert: true }
        )
        order_count_message = `Đơn hàng thứ: ${site_next_index.current_index.toString()} trong ngày`
    }

    const bill_template = get_bill_by_template(template)
    const params = {
        site,
        hub,
        order: order.data_mapping,
        data: {
            total_paid: _.sumBy(order.data_mapping?.payments || [], 'total'),
            order_count_message,
            status: order.status,
            created_at: moment(order.data_mapping.order_time).format('DD-MM-YYYY hh:mm:ss A'),
            printed_at: moment().format('DD-MM-YYYY hh:mm:ss A'),
            pick_time: order.shipment?.schedule?.from_time ? `${moment(order.shipment?.schedule?.from_date_time).format('DD/MM HH:mm')}  - ${order.shipment?.schedule?.to_time}` : '',
            qrcode,
        }
    }
    if (process.env.NODE_ENV === "prod") {
        if (['local', 'he', 'momo'].includes(order.source) && template === 'bill_for_payment') {
            const transaction = await nexdorpay.create_payment_qrcode({
                order_id: order.order_id,
                total: Math.ceil(order.data_mapping.total_for_biz),
            })
            await OrderPayment.create({
                order_id: order.order_id,
                transaction_id: transaction.transaction_id,
                vendor: 'nexdorpay',
                status: 'PENDING',
                amount: Math.ceil(order.data_mapping.total_for_biz),
                description: 'Đang chờ thanh toán',
                payment_data: {},
            })
            params.data.qrcode = transaction.qrcode
        }
    }

    if (site.getToken('dpoint')?.username === 'cyber-kitchen') {
        params.data.dpoint_qrcode = `https://member.ooo.com.vn?orderCode=${order.external_id}`
    }

    const html_preview = _.template(bill_template)(params);

    // Convert html to png
    try {
        const bill_resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://tools.nexpos.io/convert',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: {
                order_code: order.order_id,
                html_string: html_preview
            }
        })
        let labels = [];
        const raw_dishes = order?.data_mapping?.dishes ?? []
        const flatten_dishes = raw_dishes?.flatMap?.(dish => Array.from({ length: dish.quantity }, () => dish))
        if (flatten_dishes?.length > 0) {
            const total_items = flatten_dishes.length
            const promise = []
            flatten_dishes.forEach((item, index) => {
                const options = _.flatMap(item?.options ?? []).map(v => v.option_item)
                const template_params = {
                    item,
                    options,
                    order_id: order.data_mapping.order_id,
                    order_time: moment(order.data_mapping.order_time).format('DD-MM-YYYY HH:mm'),
                    source: order.source,
                    note: item.note,
                    total_items,
                    no: index + 1,
                }
                const label_html = _.template(get_bill_by_template('label'))(template_params);
                const dish_number = index + 1
                promise.push(
                    axios({
                        method: 'post',
                        maxBodyLength: Infinity,
                        url: 'https://tools.nexpos.io/convert',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        data: {
                            order_code: order.order_id + '_' + dish_number + '_label',
                            html_string: label_html
                        }
                    }).then((response, index) => ({ response, index }))
                )
            })
            const label_resps = await Promise.all(promise)
            labels = label_resps.sort((a, b) => a.index - b.index).map(v => v.response.data)
        }

        if (use_printer) {
            // TODO: remove this PrintQueue later
            await PrintQueue.create({
                site_id: order.site_id,
                hub_id: order.hub_id,
                order_id: order.order_id,
                order_print_count: printed_order ? (printed_order.order_print_count + 1) : 1,
                source: order.source,
                print_type: 'kitchen',
                status: "created",
                data: order.data_mapping,
                file_url: bill_resp.data,
            })
        }
        if (auto_print) {
            await send_message_to_topic({
                topic: 'print_order',
                message: `Order ${order.order_id} - ${hub.name} is ready to print 2`,
                data: {
                    hub_code: hub.code,
                    order_id: order.order_id,
                    site_id: order.site_id,
                    file_url: bill_resp.data,
                    label_urls: labels,
                }
            })
        }

        if (send_notification && process.env.NODE_ENV === "prod") {
            if (site.slack_channel) {
                await send_file_to_slack({ url: bill_resp.data, channel: site.slack_channel })
            }

            const zalo_group = site.zalo_group || hub.zalo_group
            if (zalo_group && zalo_group?.startsWith('https://zalo.me/g/')) {
                await send_file_to_zalo({ url: bill_resp.data, group_link: hub?.zalo_group })
            }
        }

        return {
            bill: bill_resp.data,
            labels,
        }

    } catch (error) {
        console.log(error.message)
        return {
            success: false,
            error: error.message
        }
    }
}

exports._print_order = this.print_order_v2


exports.print_order = async (req, res) => {
    const { order_id } = req.params
    const { template, preview } = req.query

    await this.generate_order_bills(order_id, { bill_type: template, generate_bill: true })
    if (preview !== 'true') {
        await this.send_order_bill_to_channel(order_id, {
            bill_template: template,
            zalo: false,
            bill: true,
            labels: { 'bill_for_kitchen': true, 'bill_for_label': true }[template] || false,
        })
    }

    const order = await Order.findOne({ order_id })

    res.json({
        success: true,
        data: {
            bill: order.bill_url,
            labels: order.label_urls,
        }
    })
}

exports.print_order_to_image_for_ecom = async (req, res) => {
    const { order_id } = req.params
    const order = await Order.findOne({ order_id })
    const site = await Site.findById(order.site_id)
    const merchant_functions = {
        shopee_ecom: shopee_ecom.print_order_bill,
        lazada: lazada.print_order_bill,
        tiktok: tiktok.print_order_bill,
    }
    const merchant_function = merchant_functions[order.source]
    if (merchant_function) {
        const token = await token_account.get_token_by_site(site, order.source)
        const bill_receipt_buff = await merchant_function(token, order_id)
        if (bill_receipt_buff) {
            const file = await upload_file({ bucket: 'nexpos-files', key: `${order.source}_receipt/Label_${order_id}}.pdf`, buff: bill_receipt_buff });
            return res.json({
                success: true,
                data: file
            })
        }
    }

    return res.json({
        success: false,
        error: "can_not_print_order_label"
    })

}

exports.print_order_payment_bill = async (req, res) => {
    const { order_id } = req.params
    const order = await Order.findOne({ order_id })
    const total_paid = _.sumBy(order.data_mapping.payments.filter(v => v.status === 'COMPLETED'), 'total') ?? 0
    const total = order.data_mapping.total_for_biz - total_paid
    if (total < 2000) {
        return res.json({
            success: false,
            error: 'total_too_low_min_2000',
        });
    }

    const { transaction_id, qrcode, pay_url } = await nexdorpay.create_payment_qrcode({
        order_id: order.order_id,
        total: Math.ceil(total),
    })

    const file = await _print_order(order, {
        use_printer: true,
        send_notification: true,
        qrcode: qrcode,
    })

    order.data.payments.push({
        method: 'NEXDORPAY',
        total: Math.ceil(total),
        status: 'PENDING',
        note: pay_url,
    })
    await OrderPayment.create({
        order_id: order.order_id, transaction_id,
        vendor: 'nexdorpay',
        status: 'PENDING',
        amount: Math.ceil(total),
        description: 'Đang chờ thanh toán',
        payment_data: { file: file.bill, transaction_id, qrcode, pay_url, total: Math.ceil(total) },
    })
    order.data_mapping.payments = order.data.payments
    order.markModified('data')
    order.markModified('data_mapping')
    await order.save()

    res.json({
        success: true,
        data: { qrcode, pay_url, file },
    })
}

exports.vendor_sync_order = async (req, res) => {
    const { order_id } = req.params
    const { source } = req.query
    const order = await Order.findOne({ order_id, source })

    order.vendor_sync = null // Cron job will do the rest for syncing
    await order.save();

    res.json({
        success: true
    })
}
