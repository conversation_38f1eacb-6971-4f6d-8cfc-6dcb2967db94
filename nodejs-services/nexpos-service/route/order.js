const { Order, SiteOrderIndex, Site, Brand, Hub, UserCart, BrandMenu, SiteMenu, SiteMenuGroup } = require('../../.shared/database')
const moment = require('moment-timezone')
const { sumBy, toNumber, isNil } = require('lodash')
const { _print_order, send_order_bill_to_channel } = require('./merchant')
const { get_hubs_has_stocks } = require('../common/order')
const momo_mini = require('./momo_mini')
const grab_express = require('../../.shared/delivery/grab_express')
const ahamove = require('../../.shared/delivery/ahamove')
const { map_order } = require('../../.shared/merchant/mapping')
const voucher_management = require('./voucher')
const token_account = require('./token_account')
const _ = require('lodash')
const { get_phone_number_report } = require('../../.shared/merchant/pankcake')
const { PAY_VIA_WALLET_OR_BANK } = require('../../.shared/const')
const { gen_external_id } = require('../../.shared/helper')
const { print_bill, bill_templates } = require('../common/print-bill')
const { apply_gifts } = require('../common/gift')

const create_local_order_v2 = async (req, res) => {
  const { site_id, note, total_surcharge, affiliate_source, payments, voucher_code: req_voucher_code, hub_id } = req.body

  const missing_fields = ['site_id', 'payments'].filter((key) => req.body[key] === null || req.body[key] === undefined)

  if (missing_fields?.length > 0) {
    res.status(400).send({
      success: false,
      error: 'invalid_payload',
    })
  }

  const site = await Site.findById(site_id)

  const cart = await UserCart.findOne({ user_id: req.user._id, site_id, status: 'created' })
  if (!cart) {
    return res.status(400).send({
      success: false,
      error: 'cart_not_found',
    })
  }

  let voucher
  const dpoint_voucher = cart.vouchers.find((v) => v.vendor === 'dpoint')
  if (dpoint_voucher) {
    const validation = await voucher_management.validate_voucher(site, dpoint_voucher.code, cart.shipment?.to.phone)

    if (validation.error) {
      return res.status(400).send({
        success: false,
        error: validation.error,
      })
    }
    voucher = validation.voucher
  }

  const coupons = cart.vouchers.map((v) => ({ name: v.vendor, code: v.code, total: v.discount }))
  // const menu = await SiteMenuGroup.findOne({ site_id: site._id }).lean()
  // const menu_items = _build_menu_items(menu)
  // const { gifts, discount, ship_discount } = await apply_gifts({ site, menu_items, cart, option_categories: menu.option_categories, coupons, user: req.user })

  // cart.gifts = gifts.filter((v) => v.is_applicable)
  // cart.discount = discount.filter((v) => v.is_applicable)
  // cart.ship_discount = ship_discount.filter((v) => v.is_applicable)

  const shipment_service = cart.shipment?.service
  const total = cart.total
  // const total = (_.sumBy(cart.dishes, 'price') ?? 0) - (_.sumBy(cart.discount, 'amount') ?? 0) // tien hang sau km TODO: a @Thong check lai giup em voi FE

  const shipping_fee = cart?.shipping_fee ?? 0 // tien ship cuoi cung
  const dishes = cart.dishes.concat(cart.gifts).map((v) => {
    const discounts = cart.discount.filter((d) => String(d.dish_id) === String(v._id))
    return {
      id: v.id,
      code: v.code,
      image: v.image,
      from_brand_id: v.from_brand_id,
      category_name: v.category_name,
      name: v.name,
      description: v.description,
      options: v.options,
      quantity: v.quantity,
      price: v.price,
      note: [...discounts.map((d) => d.note), v.note].filter((v) => v).join(', '),
      discount: _.sumBy(discounts, (d) => toNumber(d?.amount || 0)),
      is_gift: v.is_gift,
      combo: v.combo,
    }
  })

  const total_paid = sumBy(payments ?? [], 'total')
  const total_for_biz = total + toNumber(total_surcharge ?? 0) - (voucher?.discount ?? 0) // tiền mà biz nhận được, sau khi trừ đi tiền ship (trả cho đối tác) và khuyến mãi (nếu có)

  if (total_paid !== total_for_biz) {
    return res.status(400).send({
      success: false,
      error: 'payment_is_not_complete',
    })
  }

  // check voucher
  const order_group = `${site.code?.toUpperCase() ?? 'NEXDOR'}-${moment().format('YYMMDD')}`
  const site_next_index = await SiteOrderIndex.findOneAndUpdate({ site_id: site._id, group: order_group }, { $inc: { current_index: 1 } }, { new: true, upsert: true })

  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  const created_at = moment().toISOString()
  const new_order = {
    source: 'local',
    status: 'PENDING',
    site_id,
    hub_id: hub_id ?? site.hub_id,
    order_id,
    user_id: undefined,
    data_mapping: {},
    data: {
      id: order_id,
      order_id,
      source: 'local',
      affiliate_source,
      customer_name: cart.shipment?.to?.name,
      customer_phone: cart.shipment?.to?.phone,
      customer_address: cart.shipment?.to?.address,
      customer_address_obj: cart.shipment?.to,
      // driver_name: ,
      // driver_phone,
      dishes,
      note,
      total,
      total_discount: _.sumBy(cart.discount, 'amount') + (voucher?.discount ?? 0),
      total_shipment: cart.shipment?.service?.price ?? 0, // tien ship ban dau
      total_surcharge: total_surcharge ?? 0,
      total_for_biz: total_for_biz,
      order_time: moment(created_at).toISOString(),
      order_time_sort: moment(created_at).unix(),
      delivery_time: moment(created_at).toISOString(),
      delivery_time_unix: moment(created_at).unix(),
      pick_time: moment(created_at).toISOString(),
      payments,
      raw: {
        affiliate_source,
        cart,
      },
      coupons,
      ship_discount: cart.ship_discount,
      shipment_fee: cart.shipping_fee,
    },
    payments,
    voucher,
  }

  new_order.data_mapping = map_order(new_order.source, new_order.data)

  let delivery_type = {
    'pick_up': 'pick_up',
    'take_away': 'take_away',
  }[shipment_service?.vendor] || 'delivery'

  const is_delivery = delivery_type === 'delivery'
  if (is_delivery && !hub_id) {
    // Auto assign hub if has stocks
    const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }, { categories: 1 }).lean()
    const hub_with_stocks = await get_hubs_has_stocks(
      {
        categories: brand_menu.categories,
        items: new_order.data_mapping.dishes,
        address: new_order.data_mapping.customer_address,
      },
      site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id]
    )

    const hub_to_assign = hub_with_stocks?.find((v) => v.has_stock)
    if (hub_to_assign) {
      new_order.hub_id = hub_to_assign._id
    }
  }

  const is_select_hub = site.hub_ids?.length > 0 ? site.hub_ids.includes(new_order.hub_id) : new_order.hub_id === site.hub_id


  if (shipment_service) {
    const hub = await Hub.findById(new_order.hub_id)
    const ship_fee = shipment_service?.price + (shipment_service?.option?.price ?? 0)

    new_order.shipment = {
      cod: 0,
      service: shipment_service,
      schedule: shipment_service?.raw?.schedule,
      vendor: shipment_service.vendor,
      from: {
        name: site.name,
        phone: hub.phone,
        address: hub.address,
      },
      to: {
        name: cart.shipment?.to?.name,
        phone: cart.shipment?.to?.phone,
        address: cart.shipment?.to?.address,
      },
      price: ship_fee,
    }
    new_order.data.shipment = new_order.shipment
  }

  if (new_order.data.customer_phone?.length >= 10 && (new_order.source === 'local' || new_order.source === 'he')) {
    const report = await get_phone_number_report({}, new_order.data.customer_phone)
    if (report) {
      new_order.phone_report = report
    }
  }

  const auto_confirm_order = !site.hub_ids?.length

  const brand = await Brand.findById(site.brand_id)
  const cod_token = brand.getToken('cod')
  const max_cod_setting = Number(cod_token?.site_data?.max_cod || 1000000)
  const max_cod_percentage_setting = Number(cod_token?.site_data?.max_cod_percentage || 0)

  // If order has COD and total > 1M, assign to site hub (CS)
  const cash_payments = new_order.payments.filter((v) => v.method === 'CASH')
  for (const payment of cash_payments) {
    payment.status = 'COMPLETED'
  }
  const cod_payments = new_order.payments.filter((v) => v.method === 'COD')
  const total_cod = _.sumBy(cod_payments, 'total') ?? 0
  if (total_cod > 0) {
    if (total + shipping_fee > max_cod_setting) {
      new_order.hub_id = site.hub_id
      for (const payment of cod_payments) {
        payment.status = 'WAITING_PAYMENT'
        payment.prepaid_required = true
        payment.prepaid_minimum = Math.ceil(((total + shipping_fee) * max_cod_percentage_setting) / 1000) * 1000
      }
    } else {
      for (const payment of cod_payments) {
        payment.status = 'COMPLETED'
      }
      if (auto_confirm_order) {
        new_order.status = 'DOING'
      }

    }
  } else {
    new_order.status = 'WAITING_PAYMENT'
  }

  if (!new_order.payments.some(v => v.status !== 'COMPLETED')) {
    new_order.status = 'DOING'
  }


  new_order.data_mapping = map_order(new_order.source, new_order.data)
  new_order.external_id = gen_external_id()

  try {
    let total_paid_completed = 0
    for (const payment of payments) {
      if (['CASH', 'COD'].includes(payment.method) || payment.status === 'COMPLETED') {
        total_paid_completed += payment.total
      }
    }
    const order = await Order.create(new_order)
    if (delivery_type === 'take_away' && total_paid_completed === total_for_biz) {
      await send_order_bill_to_channel(new_order.order_id, {
        bill: true,
        zalo: true,
        bill_template: 'bill_for_kitchen',
        labels: true
      })
      await send_order_bill_to_channel(new_order.order_id, {
        bill: true,
        zalo: false,
        bill_template: 'bill_for_complete',
        labels: false
      })
      await Order.findOneAndUpdate({ order_id: new_order.order_id }, { auto_printed: true, auto_print_label: true, status: 'FINISH' })
    }

    // await _print_order(order, { use_printer: true, send_notification: true })
    voucher && (await voucher_management.use_voucher(voucher.code, site))

    await UserCart.findOneAndReplace(
      {
        user_id: req.user._id,
        site_id: cart.site_id,
        status: 'created',
      },
      {},
      { upsert: true, new: true }
    )

    res.status(200).json({
      success: true,
      data: order,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error?.message ?? 'Server Error',
    })
  }
}

const save_local_draft_order_v2 = async (req, res) => {
  const { site_id, note, total_surcharge, affiliate_source, payments, hub_id } = req.body

  const site = await Site.findById(site_id)

  const cart = await UserCart.findOne({ user_id: req.user._id, site_id, status: 'created' })
  if (!cart) {
    return res.status(400).send({
      success: false,
      error: 'cart_not_found',
    })
  }

  // const shipment_service = cart.shipment?.service
  const total = Math.max((_.sumBy(cart.dishes, 'price') ?? 0) - (_.sumBy(cart.discount, 'amount') ?? 0) - (cart.voucher?.discount ?? 0), 0)
  const shipping_fee = cart?.shipping_fee ?? 0
  const dishes = (cart.dishes || []).concat(cart.gifts).map((v) => {
    const discount = cart.discount.find((d) => String(d.dish_id) === String(v._id))
    return {
      id: v.id,
      code: v.code,
      image: v.image,
      from_brand_id: v.from_brand_id,
      category_name: v.category_name,
      name: v.name,
      description: v.description,
      options: v.options,
      quantity: v.quantity,
      price: v.price,
      note: [discount?.note, v.note].filter((v) => v).join(', '),
      discount: discount?.amount ?? 0,
      is_gift: v.is_gift,
      combo: v.combo,
    }
  })

  const order_group = `${site.code?.toUpperCase() ?? 'NEXDOR'}-${moment().format('YYMMDD')}`
  const site_next_index = await SiteOrderIndex.findOneAndUpdate({ site_id: site._id, group: order_group }, { $inc: { current_index: 1 } }, { new: true, upsert: true })

  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  let status = 'DRAFT'

  const created_at = moment().toISOString()
  const new_order = {
    source: 'local',
    status,
    site_id,
    hub_id: hub_id ?? site.hub_id,
    order_id,
    user_id: undefined,
    data_mapping: {},
    data: {
      id: order_id,
      order_id,
      source: 'local',
      affiliate_source,
      customer_name: cart.shipment?.to?.name,
      customer_phone: cart.shipment?.to?.phone,
      customer_address: cart.shipment?.to?.address,
      customer_address_obj: cart.shipment?.to,
      // driver_name: ,
      // driver_phone,
      dishes,
      note,
      total,
      total_discount: _.sumBy(cart.discount, 'amount') + (cart.voucher?.discount ?? 0),
      total_shipment: shipping_fee,
      total_surcharge: total_surcharge ?? 0,
      // total_for_biz: total_for_biz,
      order_time: moment(created_at).toISOString(),
      order_time_sort: moment(created_at).unix(),
      payments,
      raw: {
        affiliate_source,
        cart,
      },
    },
    payments,
    // voucher,
  }
  new_order.data_mapping = map_order(new_order.source, new_order.data)
  new_order.external_id = gen_external_id()
  try {
    const order = await Order.create(new_order)

    await UserCart.findOneAndReplace(
      {
        user_id: req.user._id,
        site_id: cart.site_id,
        status: 'created',
      },
      {},
      { upsert: true, new: true }
    )

    res.status(200).json({
      success: true,
      data: order,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error?.message ?? 'Server Error',
    })
  }
}

const draft_order_to_cart = async (req, res) => {
  const { order_id } = req.params
  const order = await Order.findOne({ order_id, status: 'DRAFT' })
  if (!order) {
    return res.status(400).send({
      success: false,
      error: 'order_not_found',
    })
  }

  const cart = order.data.raw.cart
  cart._id = undefined
  const newCart = await UserCart.findOneAndUpdate(
    {
      user_id: req.user._id,
      site_id: order.site_id,
      status: 'created',
    },
    {
      $set: {
        ...cart,
        status: 'created',
      },
    },
    { upsert: true, new: true }
  )

  res.status(200).json({
    success: true,
    data: newCart,
  })
}

const save_local_draft_order = async (req, res) => {
  const { site_id, note, customer_name, customer_phone, customer_address, driver_name, driver_phone, dishes, total_discount, total_shipment, affiliate_source, payments } = req.body
  const missing_fields = ['site_id', 'note', 'total_discount', 'customer_name', 'customer_phone', 'customer_address', 'driver_name', 'driver_phone', 'dishes', 'affiliate_source'].filter((key) =>
    isNil(req.body[key])
  )
  if (missing_fields?.length > 0) {
    res.status(400).send({
      success: false,
      error: `invalid_payload`,
    })
  }
  const total = sumBy(Array?.isArray(dishes) ? dishes : [], 'price')
  const total_for_biz = total - toNumber(total_discount) + toNumber(total_shipment || 0)

  const site = await Site.findById(site_id)

  const order_group = `${site.code?.toUpperCase() || 'NEXDOR'}-${moment().format('YYMMDD')}`

  const site_next_index = await SiteOrderIndex.findOneAndUpdate({ site_id: site._id, group: order_group }, { $inc: { current_index: 1 } }, { new: true, upsert: true })

  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  const new_order = {
    source: 'local',
    status: 'DRAFT',
    site_id,
    hub_id: site.hub_id,
    order_id,
    user_id: req.user._id,
    data_mapping: {},
    data: {
      id: order_id,
      order_id,
      source: 'local',
      affiliate_source,
      customer_name,
      customer_phone,
      customer_address,
      driver_name,
      driver_phone,
      dishes,
      note,
      total,
      total_discount,
      total_shipment,
      total_for_biz,
      order_time: moment().toISOString(),
      order_time_sort: moment().unix(),
      payments,
      raw: {
        affiliate_source,
      },
    },
    payments,
  }
  new_order.data_mapping = map_order(new_order.source, new_order.data_mapping)
  new_order.external_id = gen_external_id()

  try {
    const order = await Order.create(new_order)
    res.status(200).json({
      success: true,
      data: order,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error?.message || 'Server Error',
    })
  }
}

const delete_local_order = async (req, res) => {
  const order_id = req.params.order_id
  if (!order_id) {
    return res.status(400).json({
      success: false,
      error: 'order_id_not_found',
    })
  }

  await Order.findOneAndDelete({ order_id, status: 'DRAFT' })
  res.json({
    success: true,
  })
}

const check_hub_stocks = async (req, res) => {
  const { items, address } = req.body

  if (!items || !address) {
    return res.status(400).json({
      success: false,
      error: 'hub_missing_fields_items_address',
    })
  }

  const hubs = await get_hubs_has_stocks(
    {
      categories: [],
      items,
      address,
    },
    []
  )
  res.json({
    success: true,
    data: hubs,
  })
}

const print_temporary_bill_for_cart = async (req, res) => {
  const { site_id, note, total_surcharge, affiliate_source, payments, voucher_code, hub_id } = req.body

  const missing_fields = ['site_id'].filter((key) => req.body[key] === null || req.body[key] === undefined)

  if (missing_fields?.length > 0) {
    return res.status(400).send({
      success: false,
      error: 'invalid_payload',
    })
  }

  const site = await Site.findById(site_id)

  const cart = await UserCart.findOne({ user_id: req.user._id, site_id, status: 'created' })
  if (!cart) {
    return res.status(400).send({
      success: false,
      error: 'cart_not_found',
    })
  }

  let voucher
  if (voucher_code) {
    const validation = await voucher_management.validate_voucher(site, voucher_code, cart.shipment?.to.phone)

    if (validation.error) {
      return res.status(400).send({
        success: false,
        error: validation.error,
      })
    }
    voucher = validation.voucher
  }

  const total = (_.sumBy(cart.dishes, 'price') ?? 0) - (_.sumBy(cart.discount, 'amount') ?? 0) // tien hang sau km

  const dishes = cart.dishes.concat(cart.gifts).map((v) => {
    const discount = cart.discount.find((d) => String(d.dish_id) === String(v._id))
    return {
      id: v.id,
      code: v.code,
      image: v.image,
      from_brand_id: v.from_brand_id,
      category_name: v.category_name,
      name: v.name,
      description: v.description,
      options: v.options,
      quantity: v.quantity,
      price: v.price,
      note: [discount?.note, v.note].filter((v) => v).join(', '),
      discount: toNumber(discount?.amount ?? 0),
      is_gift: v.is_gift,
      combo: v.combo,
    }
  })

  const total_for_biz = total + toNumber(total_surcharge ?? 0) + toNumber(cart?.shipping_fee ?? 0) - (voucher?.discount ?? 0) // tiền mà biz nhận được, sau khi trừ đi tiền ship (trả cho đối tác) và khuyến mãi (nếu có)

  const created_at = moment().toISOString()
  const new_order = {
    source: 'local',
    status: 'PENDING',
    site_id,
    hub_id: hub_id ?? site.hub_id,
    order_id: '',
    user_id: undefined,
    data_mapping: {},
    data: {
      id: '',
      order_id: '',
      source: 'local',
      affiliate_source,
      customer_name: cart.shipment?.to?.name,
      customer_phone: cart.shipment?.to?.phone,
      customer_address: cart.shipment?.to?.address,
      customer_address_obj: cart.shipment?.to,
      dishes,
      note,
      total,
      total_discount: _.sumBy(cart.discount, 'amount') + (voucher?.discount ?? 0),
      total_shipment: cart.shipment?.service?.price ?? 0, // tien ship ban dau
      total_surcharge: total_surcharge ?? 0,
      total_for_biz: total_for_biz,
      order_time: moment(created_at).toISOString(),
      order_time_sort: moment(created_at).unix(),
      delivery_time: moment(created_at).toISOString(),
      delivery_time_unix: moment(created_at).unix(),
      pick_time: moment(created_at).toISOString(),
      payments,
      raw: {
        affiliate_source,
        cart,
      },
      coupons: voucher ? [{ name: 'Voucher Dpoint', code: voucher.code, total: voucher.discount }] : [],
      ship_discount: cart.ship_discount,
      shipment_fee: cart.shipping_fee,
    },
    payments,
    voucher,
  }
  new_order.data_mapping = map_order(new_order.source, new_order.data)

  const url = await print_bill(bill_templates.bill_for_payment, new_order, site, {})

  res.json({
    success: true,
    data: url,
  })
}

module.exports = {
  save_local_draft_order,
  delete_local_order,
  check_hub_stocks,
  create_local_order_v2,
  save_local_draft_order_v2,
  draft_order_to_cart,
  print_temporary_bill_for_cart,
}