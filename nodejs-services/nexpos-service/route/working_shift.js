const { WorkingShift, Order, Hub, User } = require('../../.shared/database');
const { toNumber, sumBy } = require('lodash');
const xlsx = require('xlsx');
const moment = require('moment');
const { upload_file } = require('../../.shared/storage');
const { formatCurrency, render_shift_html, render_shift_with_payment_html } = require('../common/bill');
const _ = require('lodash');
const { send_message_to_topic } = require('../../.shared/event');

const router = {};

const map_user_to_working_shift = async (working_shift) => {
    working_shift.open_by_user = await User.findById(working_shift.open_by_user_id).select(['name', 'email']).lean()
    working_shift.closed_by_user = await User.findById(working_shift.closed_by_user_id).select(['name', 'email']).lean()

    return working_shift
}

const assign_local_order_to_shift = async (working_shift) => {
    try {
        const order_filter = {
            hub_id: working_shift.hub_id,
            status: 'FINISH',
            created_at: { $gte: working_shift.start_time }
        };

        if (working_shift.status === 'closed') {
            order_filter.created_at.$lte = working_shift.end_time;
        }

        // Fetch all required data in parallel
        const [orders, hub, close_by_user] = await Promise.all([
            Order.find(order_filter),
            Hub.findById(working_shift.hub_id).select(['name', 'address']).lean(),
            working_shift.closed_by_user_id ?
                User.findById(working_shift.closed_by_user_id).select(['name', 'email']).lean() :
                Promise.resolve(null)
        ]);

        const all_income = [];
        const report = {
            hub_name: hub.name,
            created_at: moment(working_shift.created_at).format('DD/MM/YYYY HH:mm'),
            close_by: close_by_user ? `${close_by_user?.name} - ${close_by_user?.email}` : 'System',
            total_order: orders.length,
            total_gross_received: 0,
            total_net_received: 0,
            merchants: {},
            payment_methods: {},
        };

        for (const order of orders) {
            if (['he', 'local'].includes(order.source)) {
                for (const payment of order.data_mapping.payments) {
                    all_income.push({
                        payment_method: payment.method,
                        amount: payment.total,
                        note: '',
                    });
                }
            } else {
                all_income.push({
                    payment_method: order.source,
                    amount: order.data_mapping.total_for_biz,
                    note: '',
                });
            }

            if (!report.merchants[order.source]) {
                report.merchants[order.source] = {
                    orders: [],
                    gross_received: [],
                    net_received: [],
                    total_orders: 0,
                    total_gross_received: 0,
                    total_net_received: 0,
                };
            }

            report.merchants[order.source].orders.push(_.pick(order, ['order_id']));
            report.merchants[order.source].gross_received.push(order.data_mapping.finance_data?.original_price || 0);
            report.merchants[order.source].net_received.push(order.data_mapping.finance_data?.gross_received || 0);
            report.merchants[order.source].total_orders++;
            report.merchants[order.source].total_gross_received += (order.data_mapping.finance_data?.original_price || 0);
            report.merchants[order.source].total_net_received += (order.data_mapping.finance_data?.net_received || 0);

            for (const payment of order.data_mapping.payments) {
                if (!report.payment_methods[payment.method]) {
                    report.payment_methods[payment.method] = {
                        total: 0,
                    };
                }
                report.payment_methods[payment.method].total += payment.total;
            }

            report.total_gross_received += (order.data_mapping.finance_data?.original_price || 0);
            report.total_net_received += (order.data_mapping.finance_data?.net_received || 0);
        }

        const income = Object.entries(_.groupBy(all_income, 'payment_method'))
            .map(([method, items]) => ({
                payment_method: method,
                amount: _.sumBy(items, 'amount') || 0,
                note: ''
            }));

        const total_amount = working_shift.initial_amount +
            _.sumBy(income, 'amount') -
            _.sumBy(working_shift.outcome ?? [], 'amount');

        // Update document using findOneAndUpdate to avoid version conflicts
        const updated_shift = await WorkingShift.findOneAndUpdate(
            { _id: working_shift._id },
            {
                $set: {
                    report,
                    income,
                    total_amount
                }
            },
            {
                new: true,
                runValidators: true
            }
        );

        if (!updated_shift) {
            throw new Error('Working shift not found or could not be updated');
        }

        return updated_shift;
    } catch (error) {
        console.error('Error in assign_local_order_to_shift:', error);
        throw error;
    }
};

router.get_all_hubs_working_shift = async (req, res) => {
    try {
        const hubs = await Hub.find({ _id: req.user.hubs }).select(['name', 'address']).lean();
        await Promise.all(hubs.map(async hub => {
            const working_shift = await WorkingShift.findOne({ hub_id: hub._id }).sort({ created_at: -1 });
            if (working_shift) {
                const updated_shift = await assign_local_order_to_shift(working_shift);
                const working_shift_obj = updated_shift.toObject();
                working_shift_obj.open_by_user = await User.findById(working_shift.open_by_user_id).select(['name', 'email']).lean();
                working_shift_obj.closed_by_user = await User.findById(working_shift.closed_by_user_id).select(['name', 'email']).lean();
                hub.working_shift = working_shift_obj;
            } else {
                hub.working_shift = null;
            }
        }));
        res.json({
            success: true,
            data: hubs
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.get_last_working_shift = async (req, res) => {
    try {
        const { hub_id } = req.params;

        const hub = await Hub.findById(hub_id);
        if (!hub) {
            return res.status(404).json({
                success: false,
                message: 'Hub not found'
            });
        }

        const working_shift = await WorkingShift.findOne({ hub_id }).sort({ created_at: -1 });
        if (working_shift) {
            const updated_shift = await assign_local_order_to_shift(working_shift);
            const working_shift_obj = await map_user_to_working_shift(updated_shift.toObject())

            console.log({
                updated_shift,
                working_shift_obj
            })

            return res.json({
                success: true,
                data: working_shift_obj
            });
        }
        return res.json({
            success: true,
            data: null
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.get_working_shifts = async (req, res) => {
    try {
        const { page = 1, limit = 10, status } = req.query;
        const { hub_id } = req.params;

        const hub = await Hub.findById(hub_id);
        if (!hub) {
            return res.status(404).json({
                success: false,
                message: 'Hub not found'
            });
        }
        const query = {
            hub_id
        };
        if (status) { query.status = status; }

        const options = {
            page: parseInt(page, 10),
            limit: parseInt(limit, 10),
            sort: { start_time: -1 },
        };

        const result = await WorkingShift.paginate(query, options);
        for (const shift of result.docs) {
            await assign_local_order_to_shift(shift);
            shift.open_by_user = await User.findById(shift.open_by_user_id).select(['name', 'email']).lean();
            shift.closed_by_user = await User.findById(shift.closed_by_user_id).select(['name', 'email']).lean();
        }
        res.json({
            success: true,
            data: result.docs,
            totalPages: result.totalPages,
            page: result.page,
            limit: result.limit,
            total: result.totalDocs
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.create_working_shifts = async (req, res) => {
    const {
        start_time,
        initial_amount,
        note
    } = req.body;

    const { hub_id } = req.params;

    try {
        const hub = await Hub.findById(hub_id);
        if (!hub) {
            return res.status(404).json({
                success: false,
                message: 'Hub not found'
            });
        }

        const newShift = await WorkingShift.create({
            hub_id,
            open_by_user_id: req.user._id,
            start_time: moment().toISOString(),
            initial_amount: toNumber(initial_amount),
            note,
            status: 'open'
        });
        const working_shift_obj = await map_user_to_working_shift(newShift.toObject())

        res.json({
            success: true,
            data: working_shift_obj
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.update_working_shifts = async (req, res) => {
    const { hub_id, shift_id } = req.params;

    try {
        const hub = await Hub.findById(hub_id);
        if (!hub) {
            return res.status(404).json({
                success: false,
                message: 'Hub not found'
            });
        }
        const shift = await WorkingShift.findById(shift_id);

        let total_amount = shift.initial_amount + _.sumBy(req.body.income ?? [], 'amount') - _.sumBy(req.body.outcome ?? [], 'amount');

        const updated = _.pickBy({
            end_time: new Date(),
            income: req.body.income,
            outcome: req.body.outcome,
            total_amount,
            actual_amount: req.body.actual_amount ? toNumber(req.body.actual_amount) : undefined,
            status: req.body.status,
            note: req.body.note,
            ...(req.body.status === 'closed' && { closed_by_user_id: req.user._id })
        }, _.identity);

        if (_.isEmpty(updated)) {
            return res.status(400).json({
                success: false,
                message: 'No valid fields provided for update'
            });
        }

        const updatedShift = await WorkingShift.findByIdAndUpdate(
            shift_id,
            updated,
            { new: true }
        );
        await assign_local_order_to_shift(updatedShift);

        if (!updatedShift) {
            return res.status(404).json({
                success: false,
                message: 'Working shift not found'
            });
        }
        const working_shift_obj = await map_user_to_working_shift(updatedShift.toObject())

        if (updatedShift.status === 'closed') {
            router.print_working_shifts(req, { json: console.log });
        }

        res.json({
            success: true,
            data: working_shift_obj
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.delete_working_shifts = async (req, res) => {
    try {
        const deletedShift = await WorkingShift.findByIdAndDelete(req.params.shift_id);

        if (!deletedShift) {
            return res.status(404).json({
                success: false,
                message: 'Working shift not found'
            });
        }

        res.json({
            success: true,
            message: 'Working shift deleted successfully'
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.get_working_shifts_report = async (req, res) => {
    try {
        const { hub_ids, start_time, end_time } = req.query;

        const working_shifts = await WorkingShift.find({
            hub_id: hub_ids || req.user.hubs,
            created_at: { $gte: new Date(start_time), $lte: new Date(end_time) }
        });

        const result = [];
        for (const shift of working_shifts) {
            const hub = await Hub.findById(shift.hub_id).select(['name', 'address']).lean();
            shift.open_by_user = await User.findById(shift.open_by_user_id).select(['name', 'email']).lean();
            shift.closed_by_user = await User.findById(shift.closed_by_user_id).select(['name', 'email']).lean();
            result.push({
                'Tên hub': hub.name,
                'Địa chỉ': hub.address,
                'Người mở ca': shift.open_by_user?.name,
                'Người đóng ca': shift.closed_by_user?.name,
                'Thời gian bắt đầu': shift.start_time,
                'Thời gian kết thúc': shift.end_time,
                'Số tiền ban đầu': formatCurrency(shift.initial_amount),
                'Số tiền thực tế': formatCurrency(shift.actual_amount),
                'Tiền mặt đầu ca': formatCurrency(sumBy(shift.income.filter(v => v.payment_method === 'cash'), 'amount') || 0),
                'Tiền mặt cuối ca': formatCurrency(sumBy(shift.outcome.filter(v => v.payment_method === 'cash'), 'amount') || 0),
                'Tiền CK đầu ca': formatCurrency(sumBy(shift.income.filter(v => v.payment_method === 'transfer'), 'amount') || 0),
                'Tiền CK cuối ca': formatCurrency(sumBy(shift.outcome.filter(v => v.payment_method === 'transfer'), 'amount') || 0),
                'Trạng thái': shift.status === 'closed' ? 'Đã đóng' : 'Đang mở',
            });
        }

        const worksheet = xlsx.utils.json_to_sheet(result);
        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, worksheet, 'Báo cáo ca làm việc');
        const buff = xlsx.write(workbook, { type: 'buffer' });

        const timestamp = moment().format('YYMMDDHHmm');
        const file = await upload_file({ bucket: 'nexpos-files', key: `Working_Shifts/Bao_Cao_Ca_${timestamp}.xlsx`, buff });

        res.json({
            success: true,
            data: file
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

router.print_working_shifts = async (req, res) => {
    try {
        const { hub_id, shift_id } = req.params;
        const working_shift = await WorkingShift.findById(shift_id);
        // if (working_shift?.status !== 'closed') {
        //     return res.status(400).json({
        //         success: false,
        //         message: 'Working shift is not closed yet'
        //     });
        // }

        const updated_shift = await assign_local_order_to_shift(working_shift);

        const hub = await Hub.findById(hub_id);
        const file_report = await render_shift_html({ shift_id });
        const file_payment = await render_shift_with_payment_html({ shift_id });

        await send_message_to_topic({
            topic: 'print_bill',
            message: 'Working shift is ready to print',
            data: {
                messages: [{
                    template: 'bill_for_shift',
                    hub_code: hub.code,
                    urls: [file_report, file_payment],
                }],
            }
        });

        res.json({
            success: true,
            data: [file_report, file_payment]
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
};

module.exports = router;