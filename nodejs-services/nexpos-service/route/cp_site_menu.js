const { Site, SiteMenuCategory, SiteMenuItem, SiteMenuOption, CoreProduct, SiteMenuOptionItem, SiteMenuGroup, SiteMenu, BrandMenu } = require('../../.shared/database')
const { build_menu } = require('./core_product_sync_stock')
const grab_partner = require('./cp_grab_partner')

const _mark_menu_modified = async function (site_id) {
  await SiteMenu.findOneAndUpdate({ site_id }, { site_id, modified_on: new Date() }, { upsert: true })
}

const _mark_channel_modified = async function (site_id, channel) {
  const site_menu = await SiteMenu.findOne({ site_id })
  const index = site_menu.channels.findIndex((c) => c.channel === channel)
  if (index === -1) {
    site_menu.channels.push({ channel, modified_on: new Date(), synced: false })
  } else {
    site_menu.channels[index] = { modified_on: new Date(), synced: false, channel }
  }

  await site_menu.save()
}

const _get_menu_by_channel = (menu, channel) => {
  return {
    ...menu,
    categories: menu.categories
      .map((cat) => {
        const sub_categories = cat.sub_categories
          .map((sub_cat) => {
            const sub_items = sub_cat.items.filter((item) => item.channels?.find((c) => c.channel === channel)) || []
            return {
              ...sub_cat,
              items: sub_items || [],
            }
          })
          .filter((sub_cat) => sub_cat.items.length)

        return {
          ...cat,
          items: cat.items.filter((item) => item.channels?.find((c) => c.channel === channel)) || [],
          sub_categories,
        }
      })
      .filter((cat) => cat.items.length || cat.sub_categories.length),
  }
}

exports.get_menu = async function (req, res) {
  const site_id = req.params.site_id
  const channel = req.query.channel

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  let menu = await build_menu(site_id)
  if (channel) {
    menu = _get_menu_by_channel(menu, channel)
  }

  res.json({
    success: true,
    data: menu,
  })
}

exports.create_category = async function (req, res) {
  const site_id = req.params.site_id

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  const { name, parent_id, order, active } = req.body
  if (parent_id) {
    const parent = await SiteMenuCategory.findOne({ _id: parent_id })
    if (!parent) {
      return res.json({
        success: false,
        error: 'parent_category_not_found',
      })
    }
  }

  const cat = await SiteMenuCategory.create({ site_id, name, parent_id, order: order || new Date().getTime(), active })
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
    data: cat,
  })
}

exports.update_category = async function (req, res) {
  const { site_id, category_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  let cat = await SiteMenuCategory.findOne({ _id: category_id })
  if (!cat) {
    return res.json({
      success: false,
      error: 'category_not_found',
    })
  }

  const { name, parent_id, order, active } = req.body
  // cat = { ...cat, name, parent_id, order, active }
  cat.name = name
  cat.parent_id = parent_id
  cat.order = order
  cat.active = active

  let sub_categories = []
  if (!parent_id) {
    sub_categories = await SiteMenuCategory.find({ parent_id: cat._id })
  }

  await cat.save()
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
    data: { ...cat.toJSON(), sub_categories },
  })
}

exports.delete_category = async function (req, res) {
  const { site_id, category_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  await SiteMenuCategory.deleteOne({ _id: category_id, site_id })
  await _mark_menu_modified(site_id)
  res.json({
    success: true,
  })
}

// items in categories
exports.create_item = async function (req, res) {
  const { site_id, category_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }
  const { name, code, order, unit, sources, description, images, image_preview, price, active } = req.body

  const cp = await CoreProduct.findOne({ brand_id: site.brand_id, code })
  if (!cp) {
    return res.json({
      success: false,
      error: 'core_product_not_found',
    })
  }

  if (['nguyen_lieu', 'ban_thanh_pham'].includes(cp.type)) {
    return res.json({
      success: false,
      error: 'site_menu_item_type_not_allowed',
    })
  }

  const item = await SiteMenuItem.create({ site_id, category_id, name, code, unit, sources, description, images, image_preview, price, active, order: order || new Date().getTime() })
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
    data: item,
  })
}

exports.update_item = async function (req, res) {
  const { site_id, category_id, item_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  let existed = await SiteMenuItem.findOne({ _id: item_id, category_id, site_id })
  if (!existed) {
    return res.json({
      success: false,
      error: 'item_not_found',
    })
  }

  const { name, code, order, unit, sources, description, images, image_preview, price, active } = req.body

  const updated = await SiteMenuItem.findOneAndUpdate(
    {
      _id: item_id,
      category_id,
      site_id,
    },
    {
      name,
      code,
      unit,
      sources,
      description,
      images,
      image_preview,
      price,
      active,
      order,
    },
    {
      new: true,
    }
  )
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
    data: updated,
  })
}

exports.delete_item = async function (req, res) {
  const { site_id, category_id, item_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }
  await SiteMenuItem.deleteOne({ _id: item_id, category_id, site_id })
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
  })
}

exports.update_item_sale_channel = async function (req, res) {
  const { site_id, category_id, item_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  let existed = await SiteMenuItem.findOne({ _id: item_id, category_id, site_id })
  if (!existed) {
    return res.json({
      success: false,
      error: 'item_not_found',
    })
  }

  const { channel, active, price, name, categories, ...additional } = req.body

  let index = existed.channels.findIndex((c) => c.channel === channel)
  if (index === -1) {
    existed.channels.push({ channel, active, price, categories, additional, name })
  } else {
    existed.channels[index] = { channel, active, price, categories, additional, name }
  }

  await existed.save()
  await _mark_channel_modified(site_id, channel)

  res.json({
    success: true,
    data: existed,
  })
}

exports.bulk_update_item_sale_channel = async function (req, res) {
  const { site_id } = req.params
  const { item_codes, channel } = req.body

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  const menu_items = await SiteMenuItem.find({ site_id, code: { $in: item_codes } })
  if (menu_items.length !== item_codes.length) {
    return res.json({
      success: false,
      error: 'some_menu_items_not_found',
    })
  }

  // create or update channel for each item
  for (const item of menu_items) {
    const { active, price, name } = item
    let index = item.channels.findIndex((c) => c.channel === channel)
    if (index === -1) {
      item.channels.push({ channel, active, price, additional: {}, name })
    } else {
      item.channels[index] = { channel, active, price, additional: {}, name }
    }
  }

  await SiteMenuItem.bulkWrite(
    menu_items.map((item) => ({
      updateOne: {
        filter: { _id: item._id },
        update: { channels: item.channels },
      },
    }))
  )

  await _mark_channel_modified(site_id, channel)
  res.json({
    success: true,
  })
}

exports.delete_item_sale_channel = async function (req, res) {
  const { site_id, category_id, item_id, channel } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  let existed = await SiteMenuItem.findOne({ _id: item_id, category_id, site_id })

  if (!existed) {
    return res.json({
      success: false,
      error: 'item_not_found',
    })
  }

  await SiteMenuItem.updateOne({ _id: item_id, category_id, site_id }, { $pull: { channels: { channel } } })

  await _mark_channel_modified(site_id, channel)

  res.json({
    success: true,
  })
}

exports.create_option_category = async function (req, res) {
  const { site_id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  const { name, order, active, rule, item_ids, option_items } = req.body

  const option_category = await SiteMenuOption.create({ site_id, name, active, rule, item_ids, option_items, order: order || new Date().getTime() })

  if (!option_items?.length) {
    return res.json({
      success: false,
      error: 'option_items_required',
    })
  }
  if (!item_ids?.length) {
    return res.json({
      success: false,
      error: 'item_ids_required',
    })
  }
  const core_products = await CoreProduct.find({ brand_id: site.brand_id, code: { $in: option_items.map((o) => o.code) } })

  const items = option_items.map((o) => {
    const product = core_products.find((p) => p.code === o.code)
    return {
      code: product.code,
      name: product.name,
      price: o.price,
      site_id,
      order: o.order || new Date().getTime(),
      option_id: option_category._id,
    }
  })

  await SiteMenuOptionItem.insertMany(items)
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
  })
}

exports.update_option_category = async function (req, res) {
  const { site_id, id } = req.params

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({
      success: false,
      error: 'site_not_found',
    })
  }

  const { name, order, active, rule, item_ids, option_items } = req.body

  if (!option_items) {
    return res.json({
      success: false,
      error: 'option_items_required',
    })
  }
  if (!item_ids) {
    return res.json({
      success: false,
      error: 'item_ids_required',
    })
  }

  const option = await SiteMenuOption.findOneAndUpdate({ _id: id, site_id }, { name, order, active, rule, item_ids, option_items })
  if (!option) {
    return res.json({
      success: false,
      error: 'option_category_not_found',
    })
  }

  // clean up old items
  await SiteMenuOptionItem.deleteMany({ option_id: option._id, site_id })

  // create new items
  const core_products = await CoreProduct.find({ brand_id: site.brand_id, code: { $in: option_items.map((o) => o.code) } })

  const items = option_items.map((o) => {
    const product = core_products.find((p) => p.code === o.code)
    return {
      name: product.name,
      code: product.code,
      price: o.price,
      unit: product.unit,
      sources: product.sources,
      site_id,
      order: o.order || new Date().getTime(),
      option_id: option._id,
    }
  })
  const menu_items = await SiteMenuItem.find({ site_id }).lean()

  const newItems = await SiteMenuOptionItem.insertMany(items)
  await _mark_menu_modified(site_id)

  const linked_items = item_ids
    .map((item_id) => {
      const item = menu_items.find((i) => i._id.toString() === item_id.toString())
      if (!item) {
        return null
      }
      return {
        _id: item._id,
        images: item.images,
        name: item.name,
        price: item.price,
        code: item.code,
        order: item.order,
      }
    })
    .filter(Boolean)

  const data = {
    ...option.toJSON(),
    option_items: newItems,
    linked_items,
  }

  res.json({
    success: true,
    data,
  })
}

exports.delete_option_category = async function (req, res) {
  const { site_id, id } = req.params

  await SiteMenuOption.deleteOne({ _id: id, site_id })
  await _mark_menu_modified(site_id)

  res.json({
    success: true,
  })
}

exports.publish_menu = async function (req, res) {
  const { site_id } = req.params
  const site = await Site.findById(site_id)
  const site_menu = await SiteMenu.findOne({ site_id })
  if (!site_menu) {
    return res.json({
      success: false,
      error: 'site_menu_not_found',
    })
  }

  // if (!site_menu.modified_on) {
  //   return res.json({
  //     success: true,
  //   })
  // }

  const { channel } = req.body
  const menu = await build_menu(site_id, { active: true })

  // fixme: build combo, detect by code in nex PR
  const brand_menu = {
    brand_id: site.brand_id,
    categories: menu.categories,
    option_categories: menu.option_categories.map((cat) => ({
      ...cat,
      options: cat.option_items.map((o) => ({ ...o, _id: o._id.toString() })),
      category_ids: cat.item_ids.map((i) => i.toString()),
    })),
  }
  await BrandMenu.findOneAndUpdate({ brand_id: site.brand_id }, brand_menu, { upsert: true, new: true })

  if (channel === 'he' || channel === 'local') {
    await SiteMenuGroup.findOneAndUpdate(
      { site_id },
      {
        site_id,
        categories: menu.categories.map((cat) => ({
          ...cat,
          items: cat.items
            .filter((i) => i.channels?.find((c) => c.channel === channel && c.active))
            .map((i) => {
              const channel_item = (i.channels || []).find((c) => c.channel === channel) || {}
              return {
                ...i,
                ...channel_item,
                ...(channel_item?.additional || {}),
                _id: i._id.toString(),
                id: i._id.toString(),
                code: i.code,
                combo: i.combo,
              }
            }),
          sub_categories: cat.sub_categories
            ?.filter((i) => i.channels?.find((c) => c.channel === channel && c.active))
            .map((i) => {
              const channel_item = (i.channels || []).find((c) => c.channel === channel) || {}
              return {
                ...i,
                ...channel_item,
                ...(channel_item?.additional || {}),
                _id: i._id.toString(),
                id: i._id.toString(),
                code: i.code,
                combo: i.combo,
              }
            }),
        })),
        option_categories: menu.option_categories.map((cat) => ({
          ...cat,
          options: cat.option_items.map((o) => ({ ...o, _id: o._id.toString() })),
          category_ids: cat.item_ids.map((i) => i.toString()),
        })),
      },
      { upsert: true, new: true }
    )
  }

  if (channel === 'grab_food') {
    await grab_partner.invalidate_menu(site)
  }


  site_menu.modified_on = null
  site_menu.channels.forEach((element) => {
    if (element.channel === channel) {
      element.synced = true
    }
  })

  await site_menu.save()
  menu.channels = site_menu.channels
  const menu_by_channel = _get_menu_by_channel(menu, channel)

  res.json({
    success: true,
    data: menu_by_channel,
  })
}
