const { Site, Order, HubStock, HubStockHistory, CoreProduct, SiteMenuItem } = require('../../.shared/database')
const { build_menu, populate_cp_stock } = require('../common/core-product')
const grab = require('./cp_grab_partner')

const sync_orders_stock = async (req, res) => {
  const sites = await Site.find({ use_core_product: true }, { _id: 1, brand_id: 1 }).lean()

  const orders = await Order.find({
    site_id: sites.map((v) => String(v._id)),
    status: 'FINISH',
    stock_sync: { $exists: false },
  })
    .sort({ 'data_mapping.delivery_time_unix': 1 })
    .limit(50)
    .lean()

  // update to PROCESSING to prevent duplicate
  await Order.updateMany(
    {
      _id: { $in: orders.map((o) => o._id) },
    },
    {
      stock_sync: {
        status: 'PROCESSING',
        detail: { at: new Date() },
      },
    }
  )

  for (const order of orders) {
    const detail = {}

    const dishes_and_options = order.data_mapping.dishes
      .map((d) => {
        const options = d.options?.map((o) => ({ code: o.code, quantity: o.quantity })) || []

        return [{ code: d.code, quantity: d.quantity }, ...options]
      })
      .flat()
      .reduce((acc, d) => {
        acc[d.code] = acc[d.code] || 0
        acc[d.code] += d.quantity
        return acc
      }, {})

    const core_products = await CoreProduct.find({
      code: { $in: Object.keys(dishes_and_options) },
    })

    for (const core_product of core_products) {
      const ingredients = core_product.ingredients?.length
        ? core_product.ingredients
        : [
          {
            code: core_product.code,
            amount: 1,
          },
        ]

      const hub_stocks = await HubStock.find({
        code: { $in: ingredients.map((i) => i.code) },
        locked_status: 'use_stock',
      })

      for (const hub_stock of hub_stocks) {
        const ingredient = ingredients.find((i) => i.code == hub_stock.code)
        const ordered_quantity = dishes_and_options[core_product.code] || 0
        hub_stock.quantity -= ingredient.amount * ordered_quantity
        await hub_stock.save()

        await HubStockHistory.create({
          hub_id: hub_stock.hub_id,
          code: hub_stock.code,
          from_quantity: hub_stock.quantity + ingredient.amount * ordered_quantity,
          to_quantity: hub_stock.quantity,
          updated_type: 'order',
          updated_order_id: order.order_id,
        })
      }
    }

    await Order.findByIdAndUpdate(order._id, {
      stock_sync: {
        status: 'SUCCESS',
        detail: { ...detail, at: new Date() },
      },
    })
  }

  await Promise.all(
    sites.map((site) => {
      return sync_menu_stock(site._id)
    })
  )

  res.json({
    success: true,
    data: {
      orders: orders.map((o) => o.order_id),
    },
  })
}

const sync_hub_stock_to_site_menu = async (hub_id) => {
  const hub_stocks = await HubStock.find({ hub_id })

  const sites = await Site.find({ hub_id, use_core_product: true })
  for (const site of sites) {
    await sync_menu_stock(site._id, hub_stocks)
  }
}

const sync_menu_stock = async (site_id, hub_stocks) => {
  const site = await Site.findById({
    _id: site_id,
    use_core_product: true,
  }).lean()

  if (!site) return

  // sync stock to site menu
  const site_menu = await build_menu(site_id, { active: true })
  // const hub_stocks = await HubStock.find({ hub_id: site.hub_id }) // duplicate code

  const flatten_items = []
  for (const category of site_menu.categories) {
    for (const item of category.items) {
      flatten_items.push(item)
    }

    for (const sub_category of category.sub_categories) {
      for (const item of sub_category.items) {
        flatten_items.push(item)
      }
    }
  }

  const core_products = await CoreProduct.find({
    brand_id: site.brand_id,
    status: 'active',
  })

  // site menu item should map to 1 core product, we use code to match
  // a core product has many ingredients
  // a stock of an menu item is the minimum stock of its ingredients
  // a hub stock can be 'use_stock', 'alway_active', 'alway_inactive'

  for (const item of flatten_items) {
    const core_product = core_products.find((c) => c.code == item.code)
    if (!core_product) {
      item.quantity = 0
      continue
    }

    const ingredients = core_product.ingredients?.length
      ? core_product.ingredients
      : [
        {
          code: core_product.code,
          amount: 1,
        },
      ]

    let min_stock = Infinity
    for (const ingredient of ingredients) {
      const hub_stock = hub_stocks.find((h) => h.code == ingredient.code)
      // console.log(hub_stock)
      if (!hub_stock) {
        min_stock = 0
        break
      }

      if (hub_stock.locked_status == 'alway_inactive') {
        min_stock = 0
        break
      }

      if (hub_stock.locked_status == 'alway_active') {
        continue
      }

      min_stock = Math.min(min_stock, hub_stock.quantity / ingredient.amount)
    }

    if (min_stock === Infinity) {
      item.quantity_unlimited = true
    } else {
      item.quantity = min_stock
      item.quantity_unlimited = false
    }

    await SiteMenuItem.updateOne(
      { site_id, code: item.code },
      {
        $set: {
          quantity: item.quantity,
          quantity_unlimited: item.quantity_unlimited,
        },
      }
    )
  }

  // sync menu to platform
  await sync_site_stock_to_platform(site)
}

const sync_site_stock_to_platform = async (site) => {
  if (site.tokens.find(t => t.vendor === 'grab_food')) {
    await grab.invalidate_menu(site)
  }
}

module.exports = {
  sync_orders_stock,
  sync_menu_stock,
  build_menu,
}
