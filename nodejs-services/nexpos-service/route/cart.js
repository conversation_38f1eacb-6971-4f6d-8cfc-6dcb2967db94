const moment = require('moment')
const _ = require('lodash')
const { Order, Site, UserCart, SiteMenuGroup, SiteOrderIndex, Hub, User, OrderPayment, Brand, BrandMenu, Voucher, RetailerSaleConfig } = require('../../.shared/database')
const ahamove = require('../../.shared/delivery/ahamove')
const grab_express = require('../../.shared/delivery/grab_express')
const viettel_post = require('../../.shared/delivery/viettel_post')
const payos = require('../../.shared/payment/payos')
const nexdorpay = require('../../.shared/payment/nexdorpay')
const ghn = require('../../.shared/delivery/ghn')
const { get_hubs_has_stocks, get_pickup_slots, get_shipment_slots } = require('../common/order')
const { ObjectId } = require('mongodb')

const validator = require('validator')
const voucher_point = require('./voucher')
const momo = require('../../.shared/payment/momo')
const { send_zalo_message_by_order_id } = require('../../.shared/zalo')
const token_account = require('../../.shared/token_account')
const { map_order } = require('../../.shared/merchant/mapping')
const { apply_gifts, get_sale_configs } = require('../common/gift')
const { gen_external_id } = require('../../.shared/helper')
const { SELF_PICKUP, TAKE_AWAY } = require('../../.shared/const')

let router = {}

const _build_menu_items = (menu) => {
  return [
    ...menu.categories.flatMap((cat) => cat.items.map((item) => ({ ...item, category_name: cat.name }))),
    ...menu.categories.flatMap((cat) => cat.sub_categories.flatMap((sub_cat) => sub_cat.items.map((item) => ({ ...item, category_name: cat.name, sub_category_name: sub_cat.name })))),
  ]
}

// populate gifts for user
const _populate_cart_gifts = async ({ site, user, cart, selected_gifts }) => {
  const menu = await SiteMenuGroup.findOne({ site_id: site._id }).lean()
  const menu_items = _build_menu_items(menu)
  const dpoint_voucher = cart.vouchers?.find((v) => v.vendor === 'dpoint')
  const nexpos_vouchers = cart.vouchers?.filter((v) => v.vendor === 'nexpos').map((v) => v.code)
  const brand_sale_configs = await get_sale_configs(site, nexpos_vouchers)

  const { gifts, discount, ship_discount } = await apply_gifts({ user, site, menu_items, cart, selected_gifts, option_categories: menu.option_categories, dpoint_voucher, brand_sale_configs })

  // check if any nexpos voucher is not applicable => remove
  const config_ids = [...gifts, ...discount, ...ship_discount]
    .map((v) => v.config_id)
    .filter((v) => v)
    .map((v) => String(v))
  const applied_nexpos_vouchers = config_ids
    .map((v) => brand_sale_configs.find((c) => String(c._id) === v))
    .filter((v) => v?.voucher_config?.voucher_code)
    .map((v) => v.voucher_config.voucher_code)

  cart.vouchers = cart.vouchers.filter((v) => v.vendor !== 'nexpos' || applied_nexpos_vouchers.includes(v.code))

  cart.gifts = gifts
  cart.discount = discount
  cart.ship_discount = ship_discount

  if (cart.shipping_promo && cart.shipping_promo.discount > 0) {
    cart.ship_discount.push({
      amount: cart.shipping_promo.discount,
      note: `Giảm giá ship từ mã: ${cart.shipping_promo.code}`,
      is_applicable: true,
    })
  }

  const sub_total =
    (_.sumBy(cart?.dishes, 'price') || 0) +
    (_.sumBy(
      cart?.gifts.filter((g) => g.is_applicable),
      'price'
    ) || 0)

  const total_items =
    (_.sumBy(cart?.dishes, 'quantity') || 0) +
    (_.sumBy(
      cart?.gifts.filter((g) => g.is_applicable),
      'quantity'
    ) || 0)

  const shipping_discount =
    _.sumBy(
      cart?.ship_discount.filter((d) => d.is_applicable),
      'amount'
    ) || 0

  const service_price = cart?.shipment?.service?.price ?? 0
  const option_price = cart?.shipment?.service?.option?.price ?? 0
  const shipping_fee = Math.max(service_price + option_price - shipping_discount, 0)

  const total =
    sub_total +
    shipping_fee -
    (_.sumBy(
      cart?.discount.filter((d) => d.is_applicable),
      'amount'
    ) || 0) -
    (_.sumBy(
      cart?.vouchers.filter((v) => v.vendor === 'dpoint'),
      'discount'
    ) || 0)

  cart.sub_total = sub_total
  cart.total_items = total_items
  cart.total = total
  cart.shipping_fee = shipping_fee
}

// get menu and compare with cart, see if item is still available, price is still the same
const _validate_cart_items = (cart, menu_items) => {
  const cart_items = cart.dishes.map((dish) => {
    const menu_item = menu_items.filter((v) => v.active).find((v) => String(v._id) === String(dish.item_id))
    if (!menu_item) {
      return { ...dish, error: 'NOT_FOUND' }
    }

    if (menu_item.price !== dish.unit_price) {
      return { ...dish, error: 'PRICE_CHANGED', unit_price: menu_item.price, price: menu_item.price * dish.quantity }
    }

    delete dish.error
    return dish
  })

  cart.dishes = cart_items
}

const _get_cart = async (site_id, user, req, res) => {
  const cart_owner_id = req.user._id

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({ success: false, error: 'site_not_found' })
  }

  const cart = await UserCart.findOneAndUpdate({ user_id: cart_owner_id, site_id, status: 'created' }, {}, { upsert: true, new: true })
  const menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()
  const menu_items = _build_menu_items(menu)
  _validate_cart_items(cart, menu_items)
  await _populate_cart_gifts({ site, user, cart })

  await cart.save()

  res.json({
    success: true,
    data: cart,
  })
}

const _get_total_option_price = (options = []) => {
  if (!Array.isArray(options)) {
    return 0
  }
  const raw_total_option_price = _.sum(options?.map?.((childOptions) => _.sumBy(childOptions, (option) => (option?.quantity || 1) * (option?.price || 0))))
  return isNaN(raw_total_option_price) ? 0 : raw_total_option_price
}

const _update_cart = async (site_id, user, req, res) => {
  const { note } = req.body
  if (validator.isEmpty(site_id)) {
    return res.json({ success: false, error: 'site_id_is_empty' })
  }

  const cart_owner_id = req.user._id
  const cart = await UserCart.findOneAndUpdate({ user_id: cart_owner_id, site_id, status: 'created' }, {}, { upsert: true, new: true })
  // cart.dishes = dishes
  cart.note = note

  await cart.save()

  res.json({
    success: true,
    data: cart,
  })
}

// user: user who making order, for seller app, it can be null
const _update_cart_item = async (site_id, user, req, res) => {
  // _id: cart dish id, for when update existing dish item
  // item_id: menu item id, for when add new dish item

  let { _id, quantity, note, options, item_id, gifts } = req.body

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({ success: false, error: 'site_not_found' })
  }

  const menu = await SiteMenuGroup.findOne({ site_id }).lean()
  const menu_items = _build_menu_items(menu)

  const cart_owner_id = req.user._id
  const cart = await UserCart.findOneAndUpdate({ user_id: cart_owner_id, site_id, status: 'created' }, {}, { upsert: true, new: true }).lean()

  const is_update_item = !!_id
  if (is_update_item) {
    const index = _.findIndex(cart.dishes, (v) => String(v._id) === _id && !v.is_gift)
    if (index < 0) {
      return res.json({ success: false, error: 'dish_not_found' })
    }

    const newQuantity = cart.dishes[index].quantity + (quantity || 0)
    const is_remove_item = newQuantity <= 0
    if (is_remove_item) {
      cart.dishes.splice(index, 1)
    } else {
      const total_option_price = _get_total_option_price(options || cart.dishes[index].options)

      const menu_item = _.find(menu_items, (v) => String(v._id) === cart.dishes[index].item_id)
      if (!menu_item) {
        return res.json({ success: false, error: 'menu_item_is_missing' })
      }
      const unit_price = menu_item.price
      const price = newQuantity * (unit_price + total_option_price)

      cart.dishes[index].quantity = newQuantity
      cart.dishes[index].unit_price = unit_price
      cart.dishes[index].price = price

      if (note) cart.dishes[index].note = note
      if (options) cart.dishes[index].options = options
    }
  } else {
    // if (!item_id) {
    //   return res.json({ success: false, error: 'menu_item_is_missing' })
    // }

    if (item_id) {
      const menu_item = _.find(menu_items, (v) => String(v._id) === item_id)
      if (!menu_item) {
        return res.json({ success: false, error: 'menu_item_is_missing' })
      }
      const total_option_price = _get_total_option_price(options)
      const price = quantity * (menu_item.price + total_option_price)

      const new_dish = { ...menu_item, price, quantity, unit_price: menu_item.price, note, options, _id: new ObjectId(), item_id }
      cart.dishes.push(new_dish)
    }
  }

  _validate_cart_items(cart, menu_items)
  await _populate_cart_gifts({ site, user, cart, selected_gifts: gifts || [] })

  const new_cart = await UserCart.findByIdAndUpdate(cart._id, cart, { new: true }).lean()

  res.json({
    success: true,
    data: new_cart,
  })
}

const _clone_cart_from_order = async (site_id, user, req, res) => {
  const { order_id, replace_cart = false } = req.body

  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({ success: false, error: 'site_not_found' })
  }

  const order = await Order.findOne({ order_id, site_id: site_id })
  if (!order) {
    return res.json({ success: false, error: 'order_not_found' })
  }

  const menu = await SiteMenuGroup.findOne({
    site_id: order.site_id,
  }).lean()
  const menu_items = _build_menu_items(menu)

  const cart_owner_id = user._id
  const cart = await UserCart.findOneAndUpdate({ user_id: cart_owner_id, site_id, status: 'created' }, {}, { upsert: true, new: true })

  const dishes_from_order = order.data_mapping.dishes
    .filter((d) => !d.is_gift)
    .map((dish) => {
      const menu_item = menu_items.find((v) => v.name === dish.name && v.category_name === dish.category_name)
      if (!menu_item) return null
      return {
        ...menu_item,
        price: dish.quantity * menu_item.price,
        quantity: dish.quantity,
        unit_price: menu_item.price,
        note: dish.note,
        options: dish.options,
      }
    })
    .filter((v) => v)

  // combine  dishes from order and dishes from cart, if there are same dishes, sum the quantity
  const cart_dishes = replace_cart ? [] : cart.dishes.filter((d) => !d.is_gift)
  for (const dish of dishes_from_order) {
    const index = cart_dishes.findIndex((v) => v.name === dish.name && v.category_name === dish.category_name)

    if (index >= 0) {
      cart_dishes[index].quantity += dish.quantity
      cart_dishes[index].price += dish.price
    } else {
      cart_dishes.push(dish)
    }
  }

  const updated = await UserCart.findOneAndUpdate({ _id: cart._id }, { dishes: cart_dishes }, { new: true })

  res.json({
    success: true,
    data: updated,
  })
}

const _update_cart_address = async (site_id, user, req, res) => {
  const { to } = req.body
  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({ success: false, error: 'site_not_found' })
  }

  const cart_owner_id = req.user._id
  const cart = await UserCart.findOneAndUpdate({ user_id: cart_owner_id, site_id, status: 'created' }, {}, { upsert: true, new: true })
  const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }, { categories: 1 }).lean()
  const hub_with_stocks = await get_hubs_has_stocks(
    {
      categories: brand_menu.categories,
      items: cart.dishes,
      address: to.address,
    },
    site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id]
  )

  const hub_to_assign = hub_with_stocks?.find((v) => v.has_stock)
  if (!hub_to_assign) {
    return res.json({ success: false, error: 'hub_not_found' })
  }

  // mock data for dev env
  if (process.env.NODE_ENV !== 'prod' && !hub_to_assign.phone) {
    hub_to_assign.phone = '**********'
  }
  if (!hub_to_assign.address || !hub_to_assign.name || !hub_to_assign.phone) {
    return res.json({ success: false, error: 'hub_address_or_name_or_phone_is_missing' })
  }

  cart.shipment = {
    from: {
      address: hub_to_assign.address,
      phone: hub_to_assign.phone,
      name: hub_to_assign.name,
    },
    to: {
      address: to.address,
      phone: to.phone,
      name: to.name,
      user_id: user?._id,
    },
  }

  await _populate_cart_gifts({ site, user, cart })
  await cart.save()

  res.json({
    success: true,
    data: cart,
  })
}

const _get_cart_shipment = async (site_id, user, req, res) => {
  const cart_owner_id = req.user._id
  const cart = await UserCart.findOne({ user_id: cart_owner_id, site_id, status: 'created' })
  if (!cart.shipment.from.address || !cart.shipment.from.name || !cart.shipment.from.phone) {
    return res.json({ success: false, error: 'from_address_is_missing' })
  }

  let skip_shipment = false
  if (!cart.shipment.to.address || !cart.shipment.to.name || !cart.shipment.to.phone) {
    skip_shipment = true
  }

  const site = await Site.findById(site_id)
  const he_user = await User.findById(site.he_id)
  const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }).lean()

  const result = {
    // pick_up: [],
    instant_ship: [],
    same_day_ship: [],
    two_hour_ship: [],
    schedule_ship: [],
    province_ship: [],
  }

  if (site.type === 'partner') {
    const hubs = await get_hubs_has_stocks(
      {
        categories: brand_menu.categories,
        items: cart.dishes,
        address: cart.shipment.to.address,
      },
      he_user.hubs
    )
    if (hubs.length === 0) {
      return res.json({ success: false, error: 'no_hub_has_stock' })
    }
    let hub = hubs.filter((v) => v.has_stock)[0] || hubs[0]
    const hub_phone = process.env.NODE_ENV !== 'prod' && !hub.phone ? '**********' : hub.phone

    hub.phone = hub_phone
    cart.shipment.from = {
      address: hub.address,
      phone: hub.phone,
      name: hub.name,
    }
  }
  let ahamove_shipments = []
  let grab_express_shipments = []
  let grab_express_2h_shipments = []
  let viettel_post_shipments = []
  if (!skip_shipment) {
    [ahamove_shipments, grab_express_shipments, grab_express_2h_shipments, viettel_post_shipments] = await Promise.all([
      ahamove.get_shipments(await token_account.get_token_by_site(site, 'ahamove'), cart.shipment),
      // ghn.get_shipments(brand.getToken('ghn'), cart.shipment),
      grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express'), cart.shipment, 'grab_express'),
      grab_express.get_shipments(await token_account.get_token_by_site(site, 'grab_express_2h'), cart.shipment, 'grab_express_2h'),
      viettel_post.get_shipments(await token_account.get_token_by_site(site, 'viettel_post'), cart.shipment),
    ])
  }

  // Save last shipments
  cart.shipments = [...ahamove_shipments, ...grab_express_shipments, ...grab_express_2h_shipments, ...viettel_post_shipments]

  const self_pickup_token = await token_account.get_token_by_site(site, 'self_pickup')
  if (self_pickup_token?.settings?.pick_up) {
    cart.shipments.push(SELF_PICKUP)
  }
  if (self_pickup_token?.settings?.take_away) {
    cart.shipments.push(TAKE_AWAY)
  }
  await cart.save()

  result.instant_ship.push(...grab_express_shipments.filter((v) => ['INSTANT'].includes(v.code)))
  if (result.instant_ship?.length === 0) {
    result.instant_ship.push(...ahamove_shipments.filter((v) => ['SGN-BIKE'].includes(v.code)))
  }

  result.two_hour_ship.push(...ahamove_shipments.filter((v) => ['SGN-BIKE'].includes(v.code)))
  if (result.two_hour_ship?.length === 0) {
    result.two_hour_ship.push(...grab_express_shipments.filter((v) => ['INSTANT'].includes(v.code)))
  }

  result.province_ship.push(...viettel_post_shipments)

  // Sort instant_ship by lowest price and get the first item
  result.instant_ship = result.instant_ship
    .sort((a, b) => a.price - b.price)
    .filter((v) => v.price > 0)
    .slice(0, 1)
  // result.same_day_ship = result.same_day_ship.sort((a, b) => a.price - b.price).filter(v => v.price > 0).slice(0, 1)
  result.two_hour_ship = result.two_hour_ship
    .sort((a, b) => a.price - b.price)
    .filter((v) => v.price > 0)
    .slice(0, 1)

  if (result.two_hour_ship.length > 0) {
    const slots = get_shipment_slots()
    const choice_slot = result.two_hour_ship[0]
    result.schedule_ship = slots.map((v) => ({
      vendor: choice_slot.vendor,
      code: choice_slot.code,
      price: choice_slot.price,
      name: `Khung giờ ${v.from_time} -  ${v.to_time} , ngày ${moment(v.from_date_time).format('DD/MM')} `,
      description: 'Shop sẽ liên hệ và giao hàng theo lịch hẹn của bạn',
      options: choice_slot.options || [],
      raw: { ...choice_slot.raw, schedule: v },
    }))
  }

  delete result.two_hour_ship
  // delete result.same_day_ship

  const current_time = moment().utcOffset('+07:00')
  const before_8am = current_time.isBefore(moment().set({ hour: 8, minute: 0, second: 0 }))
  const after_9pm = current_time.isAfter(moment().set({ hour: 21, minute: 0, second: 0 }))
  if (before_8am || after_9pm) {
    result.instant_ship = []
  }

  res.json({
    success: true,
    data: result,
  })
}

const _apply_voucher = async (site_id, user, req, res) => {
  const { voucher_code, phone } = req.body
  if (!voucher_code || !phone) {
    return res.json({ success: false, error: 'voucher_code_or_phone_is_missing' })
  }

  const site = await Site.findById(site_id)

  const cart = await UserCart.findOne({ user_id: user._id, site_id, status: 'created' })
  let result = {
    error: null,
    voucher_code: null,
    voucher_value: 0,
    voucher_type: null, // shipping, dpoint
  }

  const existing_voucher = cart.vouchers.find((v) => v.code === voucher_code)
  if (existing_voucher) {
    return res.json({
      success: false,
      error: 'voucher_already_applied',
    })
  }

  const dpointVoucher = await voucher_point.validate_voucher(site, voucher_code, phone)
  if (!dpointVoucher.error) {
    const apply_voucher = voucher_point.is_site_apply_voucher(site)

    if (!apply_voucher) {
      return res.status(400).json({
        success: false,
        error: 'voucher_not_configured',
      })
    }
    result.voucher_code = voucher_code
    result.voucher_value = dpointVoucher.voucher.discount
    result.voucher_type = 'dpoint'

    // apply to cart
    cart.vouchers.push({
      code: voucher_code,
      discount: dpointVoucher.voucher.discount,
      vendor: dpointVoucher.voucher.vendor,
    })
    await _populate_cart_gifts({ site, user, cart })
    await cart.save()

    return res.json({
      success: true,
      data: cart
    })
  }

  result.error = dpointVoucher.error
  return res.json({
    success: false,
    data: cart,
  })
}

router.apply_nexpos_voucher = async (req, res) => {
  const { site_id } = req.params
  const { voucher_codes } = req.body
  if (_.isNil(req?.body?.voucher_codes)) {
    return res.json({ success: false, error: 'voucher_code_is_missing' })
  }

  const site = await Site.findOne({ _id: site_id })
  // const sale_configs = await get_sale_configs(site)
  const sale_configs = await RetailerSaleConfig.find({ brand_id: site.brand_id, active: true, voucher_config: { $ne: null, $exists: true } }).lean()

  const vouchers = sale_configs.filter((v) => voucher_codes.includes(v.voucher_config?.voucher_code))
  if (vouchers.length !== voucher_codes.length) {
    return res.json({ success: false, error: 'voucher_not_found' })
  }

  const cart = await UserCart.findOne({ user_id: req.user._id, site_id, status: 'created' })
  cart.vouchers = cart.vouchers.filter((v) => v.vendor !== 'nexpos')
  cart.vouchers.push(...vouchers.map((v) => ({ code: v.voucher_config.voucher_code, discount: 0, vendor: 'nexpos' })))


  await _populate_cart_gifts({ site, user: req.user, cart })
  await cart.save()

  res.json({
    success: true,
    data: cart,
  })
}

const _check_voucher = async (site_id, user, req, res) => {
  const { voucher_code, phone } = req.body
  if (!voucher_code || !phone) {
    return res.json({ success: false, error: 'voucher_code_or_phone_is_missing' })
  }

  const site = await Site.findById(site_id)

  const cart = await UserCart.findOne({ user_id: user._id, site_id, status: 'created' })
  let result = {
    error: null,
    voucher_code: null,
    voucher_value: 0,
    voucher_type: null, // shipping, dpoint
  }

  const dpointVoucher = await voucher_point.validate_voucher(site, voucher_code, phone)
  if (!dpointVoucher.error) {
    const apply_voucher = voucher_point.is_site_apply_voucher(site)

    if (!apply_voucher) {
      return res.status(400).json({
        success: false,
        error: 'voucher_not_configured',
      })
    }
    result.voucher_code = voucher_code
    result.voucher_value = dpointVoucher.voucher.discount
    result.voucher_type = 'dpoint'

    return res.json({
      success: true,
      data: result,
    })
  }

  result.error = dpointVoucher.error
  return res.json({
    success: false,
    data: result,
  })
}

const _apply_shipping_promo = async (site_id, user, req, res) => {
  const { promo_code } = req.body
  if (!promo_code) {
    return res.json({ success: false, error: 'promo_code_is_missing' })
  }

  const cart_owner_id = req.user._id
  const cart = await UserCart.findOne({ user_id: cart_owner_id, site_id, status: 'created' })
  if (!cart.shipment.from.address || !cart.shipment.from.name || !cart.shipment.from.phone) {
    return res.json({ success: false, error: 'from_address_is_missing' })
  }

  if (!cart.shipment.to.address || !cart.shipment.to.name || !cart.shipment.to.phone) {
    return res.json({ success: false, error: 'to_address_is_missing' })
  }

  const service = cart.shipment.service
  if (!service) {
    return res.json({ success: false, error: 'shipment_service_is_missing' })
  }

  const last_shipment = cart.shipments.find((v) => v.code === service.code && v.vendor === service.vendor && v.price === service.price)
  if (!last_shipment) {
    return res.json({ success: false, error: 'shipment_service_is_incorrect' })
  }

  const site = await Site.findById(site_id)
  const brand = await Brand.findById(site.brand_id)

  const shipping_promo = {
    error: '',
    discount: 0,
    code: '',
  }
  switch (service.vendor) {
    case 'ghn': {
      const [data] = await ghn.check_promo_code(brand.getToken('ghn'), {
        ...cart.shipment,
        promo_code,
        service: last_shipment,
      })

      if (data.promo_error_message) {
        shipping_promo.error = data.promo_error_message
        shipping_promo.code = promo_code
      } else {
        shipping_promo.discount = data.voucher_discount
        shipping_promo.code = promo_code
      }

      break
    }

    case 'ahamove': {
      const [data] = await ahamove.check_promo_code(await token_account.get_token_by_site(site, 'ahamove'), {
        ...cart.shipment,
        promo_code,
        services: [{ _id: service.code }],
      })

      if (data.promo_error_message) {
        shipping_promo.error = data.promo_error_message
        shipping_promo.code = promo_code
      } else {
        shipping_promo.discount = data.voucher_discount
        shipping_promo.code = promo_code
      }

      break
    }

    default: {
      return res.json({ success: false, error: 'shipment_service_is_incorrect' })
    }
  }

  cart.shipping_promo = shipping_promo
  await _populate_cart_gifts({ site, user, cart })
  await cart.save()

  res.json({
    success: true,
    data: cart,
  })
}

const _update_cart_shipment = async (site_id, user, req, res) => {
  const { vendor, code, price, name, description, raw, to } = req.body
  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({ success: false, error: 'site_not_found' })
  }

  const cart_owner_id = req.user._id
  const cart = await UserCart.findOneAndUpdate({ user_id: String(cart_owner_id), site_id, status: 'created' }, {}, { upsert: true, new: true })

  if (vendor === null) {
    // clean shipment service
    const new_shipment = {
      ...cart.shipment.toObject(),
      service: {
        vendor: null,
      }
    }
    console.log(new_shipment)
    cart.shipment = new_shipment
    cart.shipping_fee = 0
    await _populate_cart_gifts({ site, user, cart })
    await cart.save()
    return res.json({
      success: true,
      data: cart,
    })
  }

  if (vendor === 'pick_up') {
    const self_pickup_shipment = {
      ...req.body,
      ...SELF_PICKUP,
    }

    cart.shipments.push(self_pickup_shipment)
  } else if (vendor === 'take_away') {
    const take_away_shipment = {
      ...req.body,
      ...TAKE_AWAY,
    }

    cart.shipments.push(take_away_shipment)
  } else {
    if (!cart.shipment.from.address || !cart.shipment.from.name || !cart.shipment.from.phone) {
      return res.json({ success: false, error: 'from_address_is_missing' })
    }

    if (!cart.shipment.to.address || !cart.shipment.to.name || !cart.shipment.to.phone) {
      return res.json({ success: false, error: 'to_address_is_missing' })
    }

    // Check with last shipments
    if (!cart.shipments.some((v) => v.code === code && v.vendor === vendor && v.price === price)) {
      return res.json({ success: false, error: 'shipment_service_is_incorrect' })
    }
  }

  cart.shipment.service = req.body
  if (to) {
    cart.shipment.to = to
  }

  if (raw?.schedule) {
    cart.shipment.schedule = raw.schedule
  }
  cart.shipping_fee = price
  await _populate_cart_gifts({ site, user, cart })
  await cart.save()

  res.json({
    success: true,
    data: cart,
  })
}

const _handle_create_order_item = (menu_items, dishes) => {
  if (!menu_items?.length) return []
  const dish_items = menu_items.filter((menu_item) => {
    return dishes?.some((dish) => String(menu_item._id) === String(dish._id))
  })

  return dish_items.map((dish) => {
    const currentCartDish = dishes.find((d) => String(d._id) === String(dish._id))
    const newQuantity = currentCartDish?.quantity || 1
    const unit_price = dish.price
    const price = newQuantity * unit_price

    dish.quantity = newQuantity
    dish.unit_price = unit_price
    dish.price = price
    dish.note = currentCartDish?.note || ''

    return dish
  })
}

router.get_cart_gifts = async (req, res) => {
  const { site_id } = req.params
  const site = await Site.findById(site_id)
  if (!site) {
    return res.json({ success: false, error: 'site_not_found' })
  }

  const cart = await UserCart.findOneAndUpdate({ user_id: req.user._id, site_id, status: 'created' }, {}, { upsert: true, new: true })

  const menu = await SiteMenuGroup.findOne({
    site_id: site._id,
  }).lean()
  const menu_items = _build_menu_items(menu)

  const vouchers = cart.vouchers || []
  const dpoint_voucher = vouchers.find((v) => v.vendor === 'dpoint')

  let brand_sale_configs = await RetailerSaleConfig.find({
    brand_id: site.brand_id,
    active: true,
    // check voucher_config.voucher_code is not null
    'voucher_config.voucher_code': { $ne: null },
  }).lean()

  // check for start and end date
  const now = new Date()
  brand_sale_configs = brand_sale_configs.filter((v) => {
    if (v.start_date && now < v.start_date) {
      return false
    }
    if (v.end_date && now > v.end_date) {
      return false
    }
    return true
  })

  const { gifts, discount, ship_discount } = await apply_gifts({
    user: req.user,
    site,
    menu_items,
    cart,
    option_categories: menu.option_categories,
    dpoint_voucher,
    brand_sale_configs,
  })
  const config_ids = [...gifts, ...discount, ...ship_discount]
    .map((v) => v.config_id)
    .filter((v) => v)
    .map((v) => String(v))

  res.json({
    success: true,
    data: brand_sale_configs.map((v) => ({
      _id: v._id,
      name: v.name,
      description: v.description,
      voucher_code: v.voucher_config?.voucher_code,
      apply_with_other: v.voucher_config?.apply_with_other ?? false,
      end_date: v.end_date,
      is_applicable: config_ids.includes(String(v._id)),
    })),
  })
}

router.reset_cart = async (req, res) => {
  const { site_id } = req.params
  const user = req.user
  await UserCart.findOneAndReplace({ user_id: String(user._id), site_id, status: 'created' }, {})

  res.json({
    success: true,
  })
}

// user: user who making order, for seller app, it can be null
const _create_order = async (site_id, user, req, res) => {
  const { dishes, note, payment_method, promo_code, voucher_code: req_voucher_code, ship_voucher, hub_id: assigned_hub_id } = req.body

  if (validator.isEmpty(payment_method)) {
    return res.json({ success: false, error: 'payment_method_is_empty' })
  }

  const site = await Site.findById(site_id)
  const brand = await Brand.findById(site.brand_id)
  const menu = await SiteMenuGroup.findOne({ site_id }).lean()
  const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }).lean()
  const menu_items = _build_menu_items(menu)

  const cart_owner_id = req.user._id
  const cart = await UserCart.findOne({ user_id: cart_owner_id, site_id, status: 'created' })

  // console.log('cart', cart.shipment)
  // if (!cart.shipment.from.address || !cart.shipment.from.name || !cart.shipment.from.phone) {
  //   return res.json({ success: false, error: 'from_address_is_missing' })
  // }

  const is_pickup = cart.shipment?.service?.vendor === 'pick_up'
  if ((!is_pickup && !cart.shipment.to.address) || !cart.shipment.to.name || !cart.shipment.to.phone) {
    return res.json({ success: false, error: 'to_address_is_missing' })
  }

  if (!cart.shipment.service) {
    return res.json({ success: false, error: 'cart_shipment_service_is_missing' })
  }

  const { vendor, code, price } = cart.shipment.service
  // Check with last shipments
  const last_shipment = cart.shipments.find((v) => v.code === code && v.vendor === vendor && v.price === price)
  if (!last_shipment) {
    return res.json({ success: false, error: 'shipment_service_is_incorrect' })
  }

  const dish_items = _handle_create_order_item(menu_items, dishes)
  cart.dishes = dish_items.length ? dish_items : cart.dishes

  _validate_cart_items(cart, menu_items)
  if (cart.dishes.some((v) => v.error)) {
    return res.json({ success: false, error: 'cart_item_error', detail: cart.dishes.filter((v) => v.error).join(', ') })
  }

  // dpoint voucher
  // let voucher = null
  // const voucher_code = req_voucher_code || cart.voucher?.code
  // if (voucher_code) {
  //   if (!user?._id) {
  //     return res.json({
  //       success: false,
  //       error: 'user_not_found_for_voucher',
  //     })
  //   }
  //   const validation = await voucher_point.validate_voucher(site, voucher_code, user?.phone || cart?.shipment?.to?.phone)
  //   if (validation.error) {
  //     return res.json({
  //       success: false,
  //       error: validation.error,
  //     })
  //   }
  //   voucher = {
  //     code: voucher_code,
  //     discount: validation.voucher.discount,
  //     vendor: validation.voucher.vendor,
  //   }
  // }

  // shipping voucher
  let shipping_promo = { code: '', error: '', discount: 0 }
  if (ship_voucher) {
    const service = cart.shipment?.service
    if (!service) {
      return res.json({ success: false, error: 'shipment_service_is_missing' })
    }

    const last_shipment = cart.shipments.find((v) => v.code === service.code && v.vendor === service.vendor && v.price === service.price)
    if (!last_shipment) {
      return res.json({ success: false, error: 'shipment_service_is_incorrect' })
    }

    switch (service.vendor) {
      case 'ghn': {
        const [data] = await ghn.check_promo_code(brand.getToken('ghn'), {
          ...cart.shipment,
          promo_code: ship_voucher,
          service: last_shipment,
        })

        if (data.promo_error_message) {
          return res.json({ success: false, error: data.promo_error_message })
        } else {
          shipping_promo = {
            code: ship_voucher,
            discount: data.voucher_discount,
            vendor: service.vendor,
          }
        }

        break
      }

      case 'ahamove': {
        const [data] = await ahamove.check_promo_code(await token_account.get_token_by_site(site, 'ahamove'), {
          ...cart.shipment,
          promo_code: ship_voucher,
          services: [{ _id: service.code }],
        })

        if (data.promo_error_message) {
          return res.json({ success: false, error: data.promo_error_message })
        } else {
          shipping_promo = {
            code: ship_voucher,
            discount: data.voucher_discount,
            vendor: service.vendor,
          }
        }

        break
      }
    }
  }
  cart.shipping_promo = shipping_promo

  const vouchers = cart.vouchers || []
  const dpoint_voucher = vouchers.find((v) => v.vendor === 'dpoint')
  const nexpos_vouchers = vouchers.filter((v) => v.vendor === 'nexpos').map((v) => v.code)
  const brand_sale_configs = await get_sale_configs(site, nexpos_vouchers)
  const { gifts, discount, ship_discount } = await apply_gifts({ user, site, menu_items, cart, option_categories: menu.option_categories, dpoint_voucher, brand_sale_configs })
  cart.gifts = gifts.filter((v) => v.is_applicable)
  cart.discount = discount.filter((v) => v.is_applicable)
  cart.ship_discount = ship_discount.filter((v) => v.is_applicable)

  const order_group = `${site.code?.toUpperCase() || 'NEXDOR'}-${moment().format('YYMMDD')}`

  const site_next_index = await SiteOrderIndex.findOneAndUpdate({ site_id: site._id, group: order_group }, { $inc: { current_index: 1 } }, { new: true, upsert: true })

  const order_id = `${order_group}-${site_next_index.current_index.toString().padStart(3, '0')}`

  const total = Math.max((_.sumBy(cart.dishes, 'price') || 0) - (_.sumBy(cart.discount, 'amount') || 0) - (dpoint_voucher?.discount || 0), 0)
  const shipping_fee = cart?.shipping_fee || 0
  let order_time = moment()
  const cart_dishes = cart.dishes.concat(cart.gifts).map((v) => {
    const discounts = cart.discount.filter((d) => d.dish_id === v._id)
    return {
      id: v.id,
      code: v.code,
      image: v.image,
      from_brand_id: v.from_brand_id,
      category_name: v.category_name,
      name: v.name,
      description: v.description,
      options: v.options,
      quantity: v.quantity,
      price: v.price,
      note: [...discounts.map((d) => d.note), v.note].filter((v) => v).join(', '),
      discount: _.sumBy(discounts, (d) => _.toNumber(d?.amount || 0)),
      is_gift: v.is_gift,
      combo: v.combo,
    }
  })

  const assigned_hub = await Hub.findById(assigned_hub_id || site.hub_id)

  const order = {
    source: 'he',
    site_id: site._id,
    status: 'PENDING',
    order_id: order_id,
    hub_id: assigned_hub._id,
    he_id: site.he_id,
    data_mapping: {},
    vouchers: vouchers,
    shipment: {
      cod: payment_method === 'CASH' ? total : 0,
      payment_method,
      price: shipping_fee,
      note: '',
      promo: cart.shipping_promo,
      shipment_id: undefined,
      tracking_url: undefined,
      discount: cart.ship_discount,
      service: cart.shipment.service,
      schedule: cart.shipment.schedule,
      vendor: cart.shipment.service.vendor,
      from: {
        address: assigned_hub?.address,
        phone: assigned_hub?.phone,
        name: assigned_hub?.name,
      },
      to: cart.shipment.to,
    },
    data: {
      id: order_id,
      order_id: order_id,
      source: 'he',
      order_time: order_time.toISOString(),
      pick_time: null,
      delivery_time: null,
      order_time_sort: order_time.unix(),
      driver_name: '',
      driver_phone: '',
      dishes: cart_dishes,
      commission: 0,
      total: total,
      total_for_biz: total + shipping_fee,
      total_shipment: shipping_fee,
      // shipment: cart.shipment,
      promo_code: promo_code, // shipment code
      total_discount: 0,
      total_display: total.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
      affiliate_source: '',
      note: cart.note || note,
      cancel_reason: '',
      payments: [{ method: payment_method, total: total + shipping_fee, status: 'PENDING' }],
      discount: cart.discount, // discount for retailer program
      ship_discount: cart.ship_discount,
      cart: cart,
      shipment_fee: cart.shipping_fee,
      payment_method: payment_method,
      customer_address: cart.shipment.to.address,
      customer_name: cart.shipment.to.name,
      customer_phone: cart.shipment.to.phone,
      coupons: vouchers.map((v) => ({ code: v.code, total: v.discount, name: v.vendor })),
    },
    user_id: user?._id,
  }

  const order_notes = [order.data.note]

  const he = await User.findById(site.he_id)
  if (!he) {
    return res.json({ success: false, error: 'he_not_found_for_site' })
  }

  if (is_pickup || assigned_hub_id) {
    order_notes.push(`Đơn hàng được chỉ định lấy tại cửa hàng ${assigned_hub?.name}`)
  } else {
    let hub_with_stocks = await get_hubs_has_stocks(
      {
        categories: brand_menu.categories,
        items: order.data.dishes,
        address: order.data.customer_address,
      },
      site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id]
    )

    const nearest_hub_has_stock = hub_with_stocks?.find((v) => v.has_stock)
    if (nearest_hub_has_stock) {
      order.hub_id = nearest_hub_has_stock._id
      order.shipment.from = {
        address: nearest_hub_has_stock.address,
        phone: nearest_hub_has_stock.phone,
        name: nearest_hub_has_stock.name,
      }
      order_notes.push(`Đơn hàng được chỉ định lấy tại cửa hàng ${nearest_hub_has_stock?.name} theo kho hàng gần nhất`)
    } else if (hub_with_stocks?.length > 0) {
      order_notes.push(`Đơn hàng không có cửa hàng nào có đủ mặt hàng: ${hub_with_stocks[0]?.missing_items?.map((v) => v.name)?.join('\n')}`)
    }
  }
  order.data.note = order_notes.join('. ')

  const cod_token = brand.getToken('cod')
  const max_cod_setting = Number(cod_token?.site_data?.max_cod || 1000000)
  const max_cod_percentage_setting = Number(cod_token?.site_data?.max_cod_percentage || 0)

  // If order has COD and total > 1M, assign to site hub (CS)
  if (payment_method === 'COD' && total + shipping_fee > max_cod_setting) {
    order.hub_id = site.hub_id
  }

  if (payment_method === 'COD') {
    order.data.payments.forEach((v) => {
      if (total + shipping_fee >= max_cod_setting) {
        v.status = 'WAITING_PAYMENT'
        v.prepaid_required = true
        v.prepaid_minimum = Math.ceil(((total + shipping_fee) * max_cod_percentage_setting) / 1000) * 1000
      } else {
        order.status = 'DOING'
        v.status = 'COMPLETED'
      }
    })
  } else if (payment_method === 'CASH') {
    order.data.payments.forEach((v) => {
      v.status = 'COMPLETED'
    })
    order.status = 'DOING'
  } else {
    order.status = 'WAITING_PAYMENT'
  }

  order.data_mapping = map_order('he', order.data)
  order.external_id = gen_external_id()
  const new_order = await Order.create(order)

  cart.status = 'completed'
  await cart.save()

  if (dpoint_voucher) {
    await voucher_point.use_voucher(dpoint_voucher, site)
  }

  // const exist_dishes = _.differenceWith(cart.dishes, checkout_dishes, (a, b) => a._id === b._id)
  await UserCart.findOneAndUpdate({ user_id: cart.user_id, site_id: cart.site_id, status: 'created' }, {}, { upsert: true, new: true })

  res.json({
    success: true,
    data: new_order,
  })
}

router.get_cart = async (req, res, next) => {
  const { site_id } = req.params
  return _get_cart(site_id, req.user, req, res)
}

router.update_cart = async (req, res, next) => {
  const { site_id } = req.params
  return _update_cart(site_id, req.user, req, res)
}

router.update_cart_item = async (req, res, next) => {
  const { site_id } = req.params
  return _update_cart_item(site_id, req.user, req, res)
}

router.clone_cart_from_order = async (req, res, next) => {
  const { site_id } = req.params
  return _clone_cart_from_order(site_id, req.user, req, res)
}

router.update_cart_address = async (req, res, next) => {
  const { site_id } = req.params
  return _update_cart_address(site_id, req.user, req, res)
}

router.get_cart_shipment = async (req, res, next) => {
  const { site_id } = req.params
  return _get_cart_shipment(site_id, req.user, req, res)
}

router.apply_promo_code = async (req, res, next) => {
  const { site_id } = req.params
  return _apply_shipping_promo(site_id, req.user, req, res)
}

router.apply_voucher = async (req, res, next) => {
  const { site_id } = req.params
  return _apply_voucher(site_id, req.user, req, res)
}

router.check_voucher = async (req, res, next) => {
  const { site_id } = req.params
  return _check_voucher(site_id, req.user, req, res)
}

router.update_cart_shipment = async (req, res, next) => {
  const { site_id } = req.params
  return _update_cart_shipment(site_id, req.user, req, res)
}

// router.get_cart_gifts = async (req, res) => {
//   const { site_id } = req.params
//   return _get_cart_gifts(site_id, req.user, req, res)
// }

// router.apply_voucher = async (req, res) => {
//   const { site_id } = req.params
//   return _apply_voucher(site_id, req.user, req, res)
// }

router.create_order = async (req, res) => {
  const { site_id } = req.params
  return _create_order(site_id, req.user, req, res)
}

const _check_user = async (req, res) => {
  const { user_id } = req.query
  // Partner can order for customer, include guest
  let user

  if (user_id) {
    user = await User.findById(user_id)
    if (!user) {
      return res.json({ success: false, error: 'user_not_found' })
    }
  }
  return user
}

router.partner_get_cart = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _get_cart(site_id, user, req, res)
}

router.partner_update_cart = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _update_cart(site_id, user, req, res)
}

router.partner_update_cart_item = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _update_cart_item(site_id, user, req, res)
}

router.partner_update_cart_address = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _update_cart_address(site_id, user, req, res)
}

router.partner_get_cart_shipment = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _get_cart_shipment(site_id, user, req, res)
}

router.partner_apply_promo_code = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _apply_shipping_promo(site_id, user, req, res)
}

router.partner_apply_voucher = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _apply_voucher(site_id, user, req, res)
}

router.partner_update_cart_shipment = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _update_cart_shipment(site_id, user, req, res)
}

// router.partner_apply_voucher = async (req, res, next) => {
//   const { site_id } = req.params
//   const user = await _check_user(req, res)
//   return _apply_voucher(site_id, user, req, res)
// }

router.partner_create_order = async (req, res, next) => {
  const { site_id } = req.params
  const user = await _check_user(req, res)
  return _create_order(site_id, user, req, res)
}

// for order handling area
router.re_payment_for_order = async (req, res, next) => {
  const { payment_method } = req.query

  if (validator.isEmpty(payment_method)) {
    return res.json({ success: false, error: 'payment_method_is_empty' })
  }

  const order = await Order.findOne({ order_id: req.params.order_id })
  if (!order) {
    return res.json({ success: false, error: 'order_not_found' })
  }

  if (!['PENDING', 'WAITING_PAYMENT'].includes(order.status)) {
    return res.json({ success: false, error: 'order_in_process_can_not_payment' })
  }

  order.data.payment_method = payment_method
  order.status = req.query.payment_method === 'CASH' ? 'PENDING' : 'WAITING_PAYMENT'
  order.markModified('data')
  await order.save()

  const site = await Site.findById(order.site_id)
  const brand = await Brand.findById(site.brand_id)

  let momo_token = site.getToken('momo')
  if (!momo_token?.username) {
    momo_token = brand.getToken('momo')
  }

  if (['MOMO', 'MOMO_VTS', 'MOMO_ATM'].includes(req.query.payment_method)) {
    try {
      const payment_link = await momo.create_payment_link(momo_token, {
        order_id: order.order_id,
        total: Math.ceil(order.data_mapping.total_for_biz),
        payment_method: req.query.payment_method,
      })

      if (!payment_link.qrCodeUrl && payment_link?.pay_url) {
        payment_link.qrCodeUrl = await momo.get_momo_qr_code_buffer(payment_link?.pay_url)
      }

      await OrderPayment.findOneAndUpdate(
        { order_id: order.order_id },
        {
          vendor: 'momo',
          transaction_id: payment_link.transaction_id,
          amount: Math.ceil(order.data_mapping.total_for_biz),
          status: 'PENDING',
          request_data: payment_link,
        },
        { upsert: true }
      )

      return res.json({
        success: true,
        data: {
          pay_url: req.useragent?.isMobile && payment_link?.deeplink ? payment_link?.deeplink : payment_link?.pay_url,
          qr_code: payment_link.qrCodeUrl || '',
        },
      })
    } catch (error) {
      console.log(error)
      return res.json({
        success: false,
        error,
      })
    }
  }

  return res.json({ success: true })
}

router.get_site_payment_services = async (req, res, next) => {
  const { site_id } = req.params
  if (validator.isEmpty(site_id)) {
    return res.json({ success: false, error_code: 'site_not_found' })
  }

  const site = await Site.findById(site_id, { brand_id: 1 })
  const brand = await Brand.findById(site.brand_id)
  const momo_token = brand.getToken('momo')
  const payos_token = brand.getToken('payos')
  const momo_qr_code_token = brand.getToken('momo_qr_code')
  const bank_qr_code_token = brand.getToken('bank_qr_code')
  const cod_token = brand.getToken('cod')
  const others = brand.getToken('others')

  const cart = await UserCart.findOne({ user_id: req.user._id, site_id, status: 'created' })

  let result = [...DEFAULT_PAYMENT_SERVICES]
  if (others?.site_data?.payment_methods?.length > 0) {
    result = result.filter(v => others.site_data.payment_methods.some(s => s.vendor === v.vendor))
  }


  const max_cod_setting = Number(cod_token?.site_data?.max_cod || 0)
  const max_cod_percentage_setting = Number(cod_token?.site_data?.max_cod_percentage || 0)

  if (max_cod_setting > 0 && cart?.total >= max_cod_setting) {
    const item = result.find(v => v.vendor === 'COD')
    item.description = `Vui lòng đặt cọc tối thiểu số tiền ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
      Math.ceil((cart?.total * max_cod_percentage_setting) / 1000) * 1000
    )}`
  }

  if (others?.site_data?.payment_services?.length > 0) {
    result = result.filter(v => others.site_data.payment_services.some(m => m.vendor === v.vendor))
  }

  if (momo_token?.username) {
    result.push({
      vendor: 'MOMO',
      name: 'Thanh toán qua MoMo',
      description: 'Thanh toán qua ứng dụng MoMo',
      is_active: true,
    })

    // result.push({
    //   vendor: 'MOMO_VTS',
    //   name: 'Thanh toán qua Ví Trả Sau MoMo',
    //   description: 'Thanh toán qua Ví Trả Sau MoMo',
    //   is_active: true,
    // })

    // result.push({
    //   vendor: 'MOMO_ATM',
    //   name: 'Thanh toán bằng thẻ ATM qua MoMo',
    //   description: 'Thanh toán bằng thẻ ATM qua MoMo',
    //   is_active: true,
    // })
  }

  if (payos_token?.username && false) {
    result.push({
      vendor: 'PAYOS',
      name: 'Thanh toán qua PayOS',
      description: 'Thanh toán qua ứng dụng PayOS',
      is_active: true,
    })
  }

  if (momo_qr_code_token?.username) {
    result.push({
      vendor: 'MOMO_QR_CODE',
      name: 'Thanh toán qua MoMo QR Code',
      description: 'Thanh toán qua MoMo QR Code',
      is_active: true,
      qr_code: momo_qr_code_token?.username,
    })
  }

  if (bank_qr_code_token?.username) {
    result.push({
      vendor: 'BANK_QR_CODE',
      name: 'Thanh toán qua QR Code ngân hàng',
      description: 'Thanh toán qua QR Code ngân hàng',
      is_active: true,
    })
  }


  return res.json({
    success: true,
    data: result,
  })
}

const DEFAULT_PAYMENT_SERVICES = [
  {
    vendor: 'CASH',
    name: 'Tiền mặt',
    description: 'Thanh toán bằng tiền mặt khi nhận hàng',
    is_active: true,
  }, {
    vendor: 'COD',
    name: 'Thanh toán khi nhận hàng',
    description: 'Vui lòng thanh toán cho tài xế sau khi bạn kiểm tra đơn hàng',
    is_active: true,
  }, {
    vendor: 'NEXDORPAY',
    name: 'NexDorPay',
    description: 'NexDorPay',
    is_active: true,
  }, {
    vendor: 'ESTEEM_GIFT',
    name: 'Esteem Gift',
    description: 'Thanh toán bằng Esteem Gift',
    is_active: true,
  }, {
    vendor: 'GOTIT',
    name: 'Gotit',
    description: 'Thanh toán bằng Gotit',
    is_active: true,
  }, {
    vendor: 'GIFTPOP',
    name: 'Giftpop',
    description: 'Thanh toán bằng Giftpop',
    is_active: true,
  }, {
    vendor: 'VOUCHER',
    name: 'Voucher',
    description: 'Thanh toán bằng Voucher',
    is_active: true,
  }, {
    vendor: 'BANK_TRANSFER',
    name: 'Chuyển khoản thủ công',
    description: 'Thanh toán bằng chuyển khoản thủ công',
    is_active: true,
  }
]

router.get_payment_services = async (req, res, next) => {
  return res.json({
    success: true,
    data: DEFAULT_PAYMENT_SERVICES,
  })
}


router.get_order_payment_urls = async (req, res, next) => {
  const order = await Order.findOne({ order_id: req.params.order_id })
  if (!order) {
    return res.json({ success: false, error: 'order_not_found' })
  }

  if (req.query.payment_method === 'CASH') {
    return res.json({ success: true })
  }

  const site = await Site.findById(order.site_id)
  const brand = await Brand.findById(site.brand_id)

  if (['MOMO', 'MOMO_VTS', 'MOMO_ATM', 'MOMO_MOD'].includes(req.query.payment_method)) {
    try {
      let momo_token = site.getToken('momo')
      if (!momo_token?.username) {
        momo_token = brand.getToken('momo')
      }

      const total = order.data_mapping.payments.find((v) => v.method === req.query.payment_method)?.total
      if (!total) {
        return res.json({ success: false, error: 'total_not_found' })
      }

      const payment_url = await momo.create_payment_link(momo_token, {
        order_id: order.order_id,
        total: Math.ceil(total),
        payment_method: req.query.payment_method,
      })

      if (!payment_url) {
        return res.json({ success: false, error: 'payment_url_not_found' })
      }

      if (!payment_url.qrCodeUrl && payment_url?.pay_url) {
        payment_url.qrCodeUrl = await momo.get_momo_qr_code_buffer(payment_url?.pay_url)
      }

      await OrderPayment.create({
        vendor: 'momo',
        order_id: order.order_id,
        transaction_id: payment_url.transaction_id,
        status: 'PENDING',
        amount: total,
        request_data: payment_url,
      })

      return res.json({
        success: true,
        data: {
          pay_url: req.useragent?.isMobile && payment_url?.deeplink ? payment_url?.deeplink : payment_url?.pay_url,
          deeplink: payment_url?.deeplink,
          transaction_id: payment_url.transaction_id,
          qr_code: payment_url.qrCodeUrl || '',
        },
      })
    } catch (error) {
      console.log(error)
      return res.json({
        success: false,
        error,
      })
    }
  }

  // Disable PayOS use NexDorPay instead
  if (req.query.payment_method === 'PAYOS' && false) {
    try {
      let payos_token = site.getToken('payos')
      if (!payos_token?.username) {
        payos_token = brand.getToken('payos')
      }

      const order_payment = await OrderPayment.findOne({ order_id: order.order_id })
      if (order_payment) {
        await payos.delete_payment_link(payos_token, order_payment.transaction_id)
      }
      const callback = `${process.env.API_BASE}/api/payos/client-callbacks`
      const payment_url = await payos.create_payment_link(payos_token, {
        client_callback: callback,
        order_id: order.order_id,
        reference_id: moment(order.created_at).unix(),
        total: Math.ceil(order.data_mapping.total_for_biz),
      })

      await OrderPayment.create({
        vendor: 'payos',
        order_id: order.order_id,
        transaction_id: payment_url.transaction_id,
        status: 'PENDING',
        amount: Math.ceil(order.data_mapping.total_for_biz),
        request_data: payment_url,
      })
      return res.json({
        success: true,
        data: {
          pay_url: payment_url.pay_url,
          deeplink: '',
          qr_code: payment_url.qr_code,
          transaction_id: payment_url.transaction_id,
        },
      })
    } catch (error) {
      console.log(error)
      return res.json({
        success: false,
        error,
      })
    }
  }

  if (req.query.payment_method === 'NEXDORPAY') {
    try {
      let nexdorpay_token = site.getToken('nexdorpay')
      if (!nexdorpay_token?.username) {
        nexdorpay_token = brand.getToken('nexdorpay')
      }

      const total = order.data_mapping.payments.find((v) => v.method === req.query.payment_method)?.total
      if (!total) {
        return res.json({ success: false, error: 'total_not_found' })
      }

      const payment_url = await nexdorpay.create_payment_qrcode({
        order_id: order.order_id,
        total: Math.ceil(total),
      })

      await OrderPayment.create({
        order_id: order.order_id,
        transaction_id: payment_url.transaction_id,
        vendor: 'nexdorpay',
        status: 'PENDING',
        description: 'Đang chờ thanh toán',
        request_data: payment_url,
      })

      return res.json({
        success: true,
        data: {
          pay_url: payment_url.pay_url,
          transaction_id: payment_url.transaction_id,
          deeplink: '',
          qr_code: payment_url.qrcode,
        },
      })
    } catch (error) {
      console.log(error)
      return res.json({
        success: false,
        error,
      })
    }
  }

  return res.json({
    success: true,
  })
}

router.get_order_transaction = async (req, res, next) => {
  const { order_id, transaction_id } = req.params
  const order = await Order.findOne({ order_id })
  if (!order) {
    return res.json({ success: false, error: 'order_not_found' })
  }

  const transaction = await OrderPayment.findOne({ order_id, transaction_id }, { _id: 1, transaction_id: 1, status: 1 })
  if (!transaction) {
    return res.json({ success: false, error: 'transaction_not_found' })
  }

  res.json({
    success: true,
    data: transaction,
  })
}
router.get_public_order_payment_urls = async (req, res, next) => {
  const order = await Order.findOne({ order_id: req.params.order_id })
  if (!order) {
    return res.redirect(`${process.env.STORE_WEB_URL}/${site.code}/payment?success=${false}&order_id=${order.order_id}&payment_method=${req.query.payment_method}`)
  }

  if (req.query.payment_method === 'CASH') {
    return res.redirect(`${process.env.STORE_WEB_URL}/${site.code}/payment?success=${true}&order_id=${order.order_id}&payment_method=${req.query.payment_method}`)
  }

  const site = await Site.findById(order.site_id)
  const brand = await Brand.findById(site.brand_id)

  if (['MOMO', 'MOMO_VTS', 'MOMO_ATM'].includes(req.query.payment_method)) {
    try {
      let momo_token = site.getToken('momo')
      if (!momo_token?.username) {
        momo_token = brand.getToken('momo')
      }

      const payment_url = await momo.create_payment_link(momo_token, {
        order_id: order.order_id,
        total: Math.ceil(order.data_mapping.total_for_biz),
        payment_method: req.query.payment_method,
      })

      if (!payment_url) {
        return res.json({ success: false, error: 'payment_url_not_found' })
      }

      await OrderPayment.create({
        vendor: 'momo',
        order_id: order.order_id,
        transaction_id: payment_url.transaction_id,
        amount: Math.ceil(order.data_mapping.total_for_biz),
        status: 'PENDING',
        request_data: payment_url,
      })

      return res.redirect(req.useragent?.isMobile && payment_url?.deeplink ? payment_url?.deeplink : payment_url?.pay_url)
    } catch (error) {
      return res.redirect(`${process.env.STORE_WEB_URL}/${site.code}/payment?success=${false}&order_id=${order.order_id}&payment_method=${req.query.payment_method}`)
    }
  }

  // Disable PayOS use NexDorPay instead
  if (req.query.payment_method === 'PAYOS' && false) {
    try {
      let payos_token = site.getToken('payos')
      if (!payos_token?.username) {
        payos_token = brand.getToken('payos')
      }

      const order_payment = await OrderPayment.findOne({ order_id: order.order_id })
      if (order_payment) {
        await payos.delete_payment_link(payos_token, order_payment.transaction_id)
      }
      const callback = `${process.env.API_BASE}/api/payos/client-callbacks`
      const payment_url = await payos.create_payment_link(payos_token, {
        client_callback: callback,
        order_id: order.order_id,
        reference_id: moment(order.created_at).unix(),
        total: Math.ceil(order.data_mapping.total_for_biz),
      })

      await OrderPayment.create({
        vendor: 'payos',
        order_id: order.order_id,
        transaction_id: payment_url.transaction_id,
        amount: Math.ceil(order.data_mapping.total_for_biz),
        status: 'PENDING',
        request_data: payment_url,
      })
      return res.redirect(payment_url.pay_url)
    } catch (error) {
      console.log(error)
      return res.json({
        success: false,
        error,
      })
    }
  }

  return res.json({
    success: true,
  })
}

router.share_order_payment_url = async (req, res, next) => {
  const { order_id, payment_method } = req.query
  res.redirect(`${process.env.API_BASE}/api/public/site/orders/${order_id}/payment_urls?payment_method=${payment_method}`)
}

router.get_user_orders = async (req, res, next) => {
  const { site_id } = req.params
  const { status } = req.query

  let filter = {
    site_id: site_id,
    user_id: req.user._id,
  }
  if (['PENDING'].includes(status)) {
    filter.$or = [
      {
        status: 'WAITING_PAYMENT',
      },
      {
        status: ['PENDING'],
        'data_mapping.order_time_sort': {
          $gte: moment.tz('Asia/Jakarta').startOf('day').unix(),
          $lte: moment.tz('Asia/Jakarta').unix(),
        },
      },
    ]
  } else {
    filter.status = status
  }

  const orders = await Order.paginate(filter, {
    page: Number(req.query.page || 1),
    limit: Number(req.query.limit || 200), // Max 100 sites
    sort: { created_at: -1 },
    customLabels: { docs: 'data' },
    projection: {
      'data_mapping.raw': 0,
    },
  })

  res.json({
    success: true,
    ...orders,
    data: orders.data.map((v) => ({
      ...v.data_mapping,
      status: v.status,
      total_shipping: v?.data?.shipment_fee || 0,
      shipment: v.shipment,
      site_id: v.site_id,
      order_status: v.status,
    })),
  })
}

router.get_pickup_slots = async (req, res, next) => {
  const { site_id } = req.params
  const result = get_pickup_slots()
  res.json({
    success: true,
    data: result,
  })
}

router.get_assignable_hubs = async (req, res, next) => {
  const { site_id } = req.params
  const cart = await UserCart.findOne({ user_id: req.user._id, site_id, status: 'created' })
  const site = await Site.findById(site_id)
  if (!cart) {
    return res.json({
      success: true,
      data: [],
    })
  }

  // if(!cart.shipment.to.address) {
  //   return res.json({
  //     success: false,
  //     error: 'to_address_is_missing'
  //   })
  // }

  const brand_menu = await BrandMenu.findOne({ brand_id: site.brand_id }).lean()
  const item_with_gifts = cart.dishes.concat(cart.gifts || [])
  const hubs_has_stocks = await get_hubs_has_stocks(
    {
      categories: brand_menu.categories,
      items: item_with_gifts,
      address: cart.shipment.to.address,
    },
    site.hub_ids?.length > 0 ? site.hub_ids : [site.hub_id]
  )

  res.json({
    success: true,
    data: hubs_has_stocks.map((v) => ({
      _id: v._id,
      name: v.name,
      code: v.code,
      address: v.address,
      has_stock: v.has_stock,
      distance: v.distance,
    })),
  })
}

module.exports = router
