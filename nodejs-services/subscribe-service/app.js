const { subscriber } = require('../.shared/pubsub');
const { generate_report_file } = require('./route/report');
const { dispatch_brand_request, cron_brand_request } = require('./route/brand_request');
const { export_brand_menu_of_all_sites } = require('./route/menu');

const start_listener = async () => {
    subscriber('REPORT', async (message) => {
        console.log('Received message:', message.data.toString());
        const data = JSON.parse(message.data.toString());
        generate_report_file(data._id);
        console.log('Message acknowledged');
        message.ack();
    });
    subscriber('EXPORT_BRAND_MENU', async (message) => {
        console.log('Received message:', message.data.toString());
        const data = JSON.parse(message.data.toString());
        export_brand_menu_of_all_sites(data)
        console.log('Message acknowledged');
        message.ack();
    });
}

const start_cron = async () => {
    setInterval(async () => {
        cron_brand_request()
    }, 60000)
}

start_listener()
start_cron()