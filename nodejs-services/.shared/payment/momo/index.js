const crypto = require('crypto');
const uuid = require('uuid');
const axios = require('axios');
const _ = require('lodash');
const cheerio = require('cheerio');
const { upload_file } = require('../../storage');

let momo = {};

const ordinary = {
    captureMoMoWallet: ['accessKey', 'amount', 'extraData', 'ipnUrl', 'orderId', 'orderInfo', 'partnerCode', 'redirectUrl', 'requestId', 'requestType'],
    transactionStatus: ['partnerCode', 'accessKey', 'requestId', 'orderId', 'requestType']
}


const sort_keys = (data, options) => {
    const itemList = ordinary[options];
    //maps and join
    const resultList = itemList.map(item => {
        let a = `${item}=${data[item]}`;
        return a;
    })
    return resultList.join("&");
}


const get_data_signature = (data, options, secretKey) => {
    let rawSignature = sort_keys(data, options);
    let signature = crypto.createHmac('sha256', secretKey)
        .update(rawSignature)
        .digest('hex');
    return signature
}

const endpoint = `${process.env.MOMO_PAYMENT_URL}/v2/gateway/api/create`;
momo.create_payment_link = async (momo_token, { order_id, total, payment_method = 'MOMO' }) => {
    const client_callback = `${process.env.API_BASE}/api/momo/client-callbacks`
    const server_callback = `${process.env.API_BASE}/api/momo/callbacks`

    const request_type = {
        MOMO: 'captureWallet',
        MOMO_VTS: 'payWithVTS',
        MOMO_ATM: 'payWithATM',
        MOMO_MOD: 'onDelivery',
    }[payment_method]
    const transaction_id = order_id + '_' + uuid.v4().split('-')[0];
    let data = {
        partnerCode: momo_token.site_id,
        accessKey: momo_token.username,
        requestId: transaction_id,
        amount: total,
        orderId: transaction_id,
        orderInfo: `Thanh toán momo cho order ${order_id}`,
        redirectUrl: client_callback,
        ipnUrl: server_callback,
        requestType: request_type,
        extraData: '',
        lang: 'vi',
    }
    let signature = get_data_signature(data, "captureMoMoWallet", momo_token.password);
    data.signature = signature;
    delete data.accessKey
    try {
        const resp = await axios({
            method: 'post',
            maxBodyLength: Infinity,
            url: `${process.env.MOMO_PAYMENT_URL}/v2/gateway/api/create`,
            headers: {
                'Content-Type': 'application/json'
            },
            data
        })
        console.log(data)
        console.log('resp.data', resp.data)
        return {
            pay_url: resp.data.payUrl,
            deeplink: resp.data.deeplink,
            transaction_id: resp.data.orderId,
            data: resp.data,
            qrCodeUrl: resp.data.qrCodeUrl
        }
    } catch (error) {
        console.log(error)
        return null
    }
}

momo.getMoMoTransactionStatus = async (momo_token, { order_id, request_id }) => {
    let options = {};
    options.partnerCode = momo_token.site_id;
    options.accessKey = momo_token.username;
    let orderId = order_id
    let requestId = request_id
    let requestType = "transactionStatus"
    let captureData = {
        ...options,
        orderId,
        requestId,
        requestType,
    }

    let signature = get_data_signature(captureData, requestType, momo_token.password);
    let body = JSON.stringify({
        ...options,
        requestId: requestId,
        orderGroupId: '',
        autoCapture: true,
        lang: 'vi',
        orderId: orderId,
        requestType: requestType,
        signature: signature,
    });
    try {
        let response = await axios.post(endpoint, body);
        let data = response.data;
        return data;
    } catch (e) {
        console.log(e)
        return null
    }
}

momo.get_momo_qr_code_buffer = async (pay_url) => {
    const resp = await axios.get(pay_url)
    const $ = cheerio.load(resp.data);


    if ($('svg').length === 0) {
        return null;
    }
    const image_b64 = $('.image-qr-code').attr('src').replace('data:image/png;base64,', '')
    const buffer = Buffer.from(image_b64, 'base64')

    const image_hash = crypto.createHash('md5').update(buffer).digest('hex')
    const file = await upload_file({
        bucket: 'nexpos-files',
        key: `momo_qr/${image_hash}.jpg`,
        buff: buffer
    })
    return file;
}

module.exports = momo