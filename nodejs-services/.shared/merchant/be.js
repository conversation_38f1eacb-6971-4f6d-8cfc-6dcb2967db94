const axios = require('../axios');
const moment = require('moment-timezone');
moment.tz.setDefault('Asia/Bangkok');
const qs = require('qs');
const _ = require('lodash');

const skip_call_api = () => moment().hour() >= 1 && moment().hour() <= 5;


const base_headers = () => {
    return {
        'user-agent': 'Dart/3.2 (dart:io)',
        'content-type': 'application/json',
        'device_type': '0'
    }
}

const base_body = ({ access_token, site_id }) => {
    const site = JSON.parse(site_id)
    return {
        "access_token": access_token,
        "app_version": "151",
        "device_token": "",
        "operator_token": "",
        "locale": "vi",
        "device_type": 0,
        "merchant_id": site.merchant_id,
        // "store_id": site.restaurant_id,
        "restaurant_id": site.restaurant_id,
        "user_id": site.user_id,
        "vendor_id": site.vendor_id,


    }
}

async function get_token(username, password) {
    if (!password) {
        return null
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/login',
            headers: base_headers(),
            data: {
                "operator_token": "",
                "device_type": "0",
                "access_token": "PENDING",
                "email": username,
                "password": password,
                "device_token": "",
                "app_version": "151",
                "locale": "vi"
            }
        }

        const resp = await axios(config);
        const access_token = resp.data.token
        const user_id = resp.data.user.user_id

        config.url = `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles`
        config.data = {
            "access_token": access_token,
            "user_id": user_id,
        }
        const resp2 = await axios(config);
        if (resp2.data.data[0].role_names?.includes('MERCHANT_OWNER')) {
            return {
                access_token,
                site_id: JSON.stringify({
                    "merchant_id": resp2.data.data[0].merchant_id,
                    "restaurant_id": null,
                    "user_id": user_id,
                    "vendor_id": null,
                    "role": 'MERCHANT_OWNER'
                })
            }
        }
        config.url = `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/store/get`
        config.data = {
            "access_token": access_token,
            "store_id": resp2.data.data[0].store_profiles[0].store_id,
            "user_id": user_id,
        }

        const resp3 = await axios(config);
        const store = resp3.data.store
        return {
            access_token,
            site_id: JSON.stringify({
                "merchant_id": store.merchant_id,
                "restaurant_id": store.store_id,
                "user_id": user_id,
                "vendor_id": store.vendor_id,
            })
        }

    } catch (error) {
        console.log(error.message)
        return null
    }
}

async function get_store_list_by_auth(username, password) {
    if (!password) {
        return null
    }

    let result = []
    try {

        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/login',
            headers: base_headers(),
            data: {
                "operator_token": "",
                "device_type": "0",
                "access_token": "PENDING",
                "email": username,
                "password": password,
                "device_token": "",
                "app_version": "151",
                "locale": "vi"
            }
        }

        const resp = await axios(config);
        const access_token = resp.data.token
        const user_id = resp.data.user.user_id

        config.url = `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles`
        config.data = {
            "access_token": access_token,
            "user_id": user_id,
        }
        const resp2 = await axios(config);
        if (resp2.data.data[0].role_names?.includes('MERCHANT_OWNER')) {
            return {
                access_token,
                site_id: JSON.stringify({
                    "merchant_id": resp2.data.data[0].merchant_id,
                    "restaurant_id": null,
                    "user_id": user_id,
                    "vendor_id": null,
                    "role": 'MERCHANT_OWNER'
                })
            }
        }
        result.merchants = resp2.data.data.map(v => ({
            merchant_id: v.merchant_id,
            merchant_name: v.merchant_name,
        }))

        for (const merchant of resp2.data.data) {
            console.log(merchant.store_profiles.map(v => v.store_name).join('\n'))
            for (const store_profile of merchant.store_profiles) {
                config.url = `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/store/get`
                config.data = {
                    "access_token": access_token,
                    "store_id": store_profile.store_id,
                    "user_id": user_id,
                }

                const resp3 = await axios(config);
                const store = resp3.data.store
                result.push({
                    access_token,
                    site_name: store_profile.store_name,
                    site_id: JSON.stringify({
                        "merchant_id": store.merchant_id,
                        "restaurant_id": store.store_id,
                        "user_id": user_id,
                        "vendor_id": store.vendor_id,
                    })
                })
            }
        }
        return result
    } catch (error) {
        console.log(error.message)
    }
    return result
}

async function get_token_by_store_name(username, password, store_name) {
    if (!password) {
        return null
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: 'https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/login',
            headers: base_headers(),
            data: {
                "operator_token": "",
                "device_type": "0",
                "access_token": "PENDING",
                "email": username,
                "password": password,
                "device_token": "",
                "app_version": "151",
                "locale": "vi"
            }
        }

        const resp = await axios(config);
        const access_token = resp.data.token
        const user_id = resp.data.user.user_id

        config.url = `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles`
        config.data = {
            "access_token": access_token,
            "user_id": user_id,
        }
        const resp2 = await axios(config);
        if (resp2.data.data[0].role_names?.includes('MERCHANT_OWNER')) {
            return {
                access_token,
                site_id: JSON.stringify({
                    "merchant_id": resp2.data.data[0].merchant_id,
                    "restaurant_id": null,
                    "user_id": user_id,
                    "vendor_id": null,
                    "role": 'MERCHANT_OWNER'
                })
            }
        }
        console.log(resp2.data.data[0].store_profiles.map(v => v.store_name).join('\n'))
        const store_profile = resp2.data.data[0].store_profiles.find(v => v.store_name.toLowerCase().includes(store_name.toLowerCase()))
        if (!store_profile) {
            return null
        }
        config.url = `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/store/get`
        config.data = {
            "access_token": access_token,
            "store_id": store_profile.store_id,
            "user_id": user_id,
        }

        const resp3 = await axios(config);
        const store = resp3.data.store
        return {
            access_token,
            site_id: JSON.stringify({
                "merchant_id": store.merchant_id,
                "restaurant_id": store.store_id,
                "user_id": user_id,
                "vendor_id": store.vendor_id,
            })
        }

    } catch (error) {
        console.log(error.message)
        return null
    }
}

const CANCEL_STATUS = [3, 33, 9, 10, 17, 21, 25];

async function get_order_list_v2({ site_id, access_token }) {
    const result = {
        success: true,
        data: {}
    }

    if (!access_token || skip_call_api()) {
        return result
    }

    try {
        const filter_data = {
            "PENDING": {
                fetch_pending_orders: 1
            },
            "DOING": {
                fetch_pending_orders: 0
            },
            "FINISH_OR_CANCEL": {
                fetch_previous_orders: 1,
                start_index: 0,
                page_size: 100,
                start_date: moment().format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
            },
        }

        for (const [status, filter] of Object.entries(filter_data)) {
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `https://gw.be.com.vn/api/v1/be-marketplace/restaurant_orders`,
                headers: base_headers(),
                data: { ...filter, ...base_body({ site_id, access_token }) }
            };

            let orders = []
            try {
                const resp = await axios(config);
                orders = resp.data.order_info.filter(v => moment(v.created_at).isSame(moment(), 'day'))
                if (status === 'FINISH_OR_CANCEL') {
                    result.data['FINISH'] = orders.filter(v => !CANCEL_STATUS.includes(v.order_status))
                    result.data['CANCEL'] = orders.filter(v => CANCEL_STATUS.includes(v.order_status)) // Get only cancelled orders, 9: cancel by merchant, 10: by admin, 21: by system
                } else {
                    result.data[status] = orders
                }
            } catch (error) {
                console.log(error)
                result.success = false
                return result
            }

        }

        for (const key of Object.keys(result.data)) {
            result.data[key] = result.data[key].map(v => _.pick(v, ['order_status', 'order_id', 'delivery_status']))
        }
        return result

    } catch (error) {
        console.log(error)
        result.success = false
        return result
    }
}


async function get_order_list_by_duration({ access_token, site_id }, { from, to }) {
    const result = {
        success: true,
        data: {
            FINISH: [],
            CANCEL: []
        }
    }
    if (!access_token) {
        return result
    }

    const data = {
        fetch_previous_orders: 1,
        start_index: 0,
        page_size: 200,
        start_date: from.format('YYYY-MM-DD'),
        end_date: to.format('YYYY-MM-DD'),
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gw.be.com.vn/api/v1/be-marketplace/restaurant_orders`,
            headers: base_headers(),
            data: { ...data, ...base_body({ site_id, access_token }) }
        };
        const resp = await axios(config);
        const orders = resp.data.order_info ?? []
        result.data['FINISH'] = orders?.filter(v => !CANCEL_STATUS.includes(v.order_status)) ?? []
        result.data['CANCEL'] = orders?.filter(v => CANCEL_STATUS.includes(v.order_status)) ?? []
    } catch (error) {
        console.error(error)
    }
    return result

}


async function confirm_order({ site_id, access_token }, order_id) {
    if (!access_token) {
        return {}
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gw.be.com.vn/api/v1/be-marketplace/process_order`,
            headers: base_headers({ access_token }),
            data: { order_id: Number(order_id), ...base_body({ site_id, access_token }) }
        };
        const resp = await axios(config);
        console.log(resp.data)
    } catch (err) {
        console.log(err)
    }
    return {}
}

async function cancel_order({ site_id, access_token }, order_id, { cancel_type = 'out_stock', cancel_reason = "" }) {
    const result = {
        success: true,
        message: "",
    }

    if (!access_token) {
        return result
    }

    try {
        const config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gw.be.com.vn/api/v1/be-marketplace/cancel_order`,
            headers: base_headers({ access_token }),
            data: {
                order_id: Number(order_id),
                reason: cancel_type === "out_stock" ? "Hết món yêu cầu" : "Quán quá tải",
                ...base_body({ site_id, access_token })
            }
        };
        const resp = await axios(config);
        console.log(resp.data)
    } catch (err) {
        console.error(err)
        result.success = false
        result.message = err.message
    }
    return result
}

async function get_order_detail({ site_id, access_token }, order_id) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-marketplace/restaurant_orders`,
        headers: base_headers({ access_token }),
        data: { order_id: Number(order_id), ...base_body({ site_id, access_token }) }
    };

    try {
        const resp = await axios(config);
        return resp.data.order_info[0]
    } catch (err) {
        console.log(err)
    }
    return null
}

async function update_store_status({ site_id, access_token }, { status, duration }) {
    if (!access_token) {
        return {}
    }
    let data = {
        order_mode: status === "close" ? 2 : 1
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://gw.be.com.vn/api/v1/be-marketplace/vendor/update_restaurant_profile',
        headers: base_headers(),
        data: {
            ...data, ...base_body({ site_id, access_token })
        }
    }

    const resp = await axios(config);
    return resp.data
}

async function get_open_status({ site_id, access_token }) {
    if (!access_token) {
        return null
    }

    try {
        let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles`,
            headers: base_headers(),
            data: base_body({ site_id, access_token })
        }
        const resp2 = await axios(config);
        return resp2.data.data[0].store_profiles[0].order_mode === 1
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_opening_hour({ site_id, access_token }) {
    if (!access_token) {
        return []
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-marketplace/vendor/restaurant_timing/get`,
        headers: base_headers(),
        data: base_body({ site_id, access_token })
    };

    try {
        const resp = await axios(config);
        return resp.data.timings
    } catch (err) {
        console.log(err)
    }
    return {}
}

async function update_opening_hour({ site_id, access_token }, working_hours) {
    if (!access_token) {
        return null
    }

    const old_timings = await get_opening_hour({ site_id, access_token })
    for (const working_hour of working_hours) {
        const old_timing = old_timings.find(v => v.day_id === working_hour.day_id)
        if (working_hour.timings.length > 0 && old_timing?.timings?.length > 0) {
            working_hour.timings[0].time_id = old_timing?.timings[0].time_id
        }
    }
    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-marketplace/vendor/restaurant_timing/update`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            timings: JSON.stringify(working_hours)
        }
    };

    try {
        const resp = await axios(config);
        return resp.data.message
    } catch (err) {
        console.log(err)
    }
    return []
}

async function get_menu({ site_id, access_token }, data) {
    const result = {
        categories: [],
        option_categories: [],
    }

    if (!access_token) {
        return result
    }

    let config1 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_restaurant_items`,
        headers: base_headers(),
        data: base_body({ site_id, access_token })
    };
    let config2 = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_item_customizes`,
        headers: base_headers(),
        data: base_body({ site_id, access_token })
    };

    try {
        const resp = await axios(config1);
        result.categories = resp.data.restaurant_items
    } catch (err) {
        console.log(err)
    }
    try {
        const resp = await axios(config2);
        result.option_categories = resp.data.item_customizes
    } catch (err) {
        console.log(err)
    }
    return result
}

async function update_menu_item({ site_id, access_token }, data) {
    if (!access_token) {
        return null
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-marketplace/vendor/update_menu_item_status`,
        headers: base_headers(),
        data: { ...base_body({ site_id, access_token }), ...data }
    };

    try {
        const resp = await axios(config);
        return resp.data.restaurant_items
    } catch (err) {
        console.log(err)
    }
}

async function get_store({ site_id, access_token }) {
    if (!access_token) {
        return {}
    }

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/store/get`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            "store_id": JSON.parse(site_id).restaurant_id,
        }
    };

    try {
        const resp = await axios(config);
        return resp.data.store
    } catch (err) {
        console.log(err)
        return null
    }
}

async function get_order_feedbacks({ site_id, access_token }, limit) {
    let result = []

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/restaurant/ratings`,
        headers: base_headers(),
        data: {
            ...base_body({ site_id, access_token }),
            page: 1,
            limit: 100,
        }
    };

    try {
        const resp = await axios(config);
        result = resp.data.ratings
    } catch (err) {
        console.log(err)
    }
    return result
}


module.exports = {
    base_headers,
    base_body,
    get_token,
    get_order_list_v2,
    get_order_list_by_duration,
    get_order_detail,
    confirm_order,
    cancel_order,
    update_store_status,
    get_open_status,
    get_opening_hour,
    update_opening_hour,
    get_menu,
    update_menu_item,
    get_store,
    get_token_by_store_name,
    get_store_list_by_auth,
    get_order_feedbacks,
}