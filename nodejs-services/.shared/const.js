
const MOMO_PAYMENT_METHODS = ['MOM<PERSON>', 'MOMO_VTS', 'MOMO_ATM', 'MOMO_MOD']


module.exports = {
    MERCHANTS: ['grab', 'grab_mart', 'shopee', 'shopee_ecom', 'shopee_fresh', 'tiktok', 'lazada', 'momo', 'gojek', 'be', 'local', 'he'],
    OTHER_MERCHANTS: [
        'Fanpage',
        'Tiktok',
        'Web',
        'Điện thoại',
        'Catering service',
        'Lazada',
        'Shopee'
    ],
    MERCHANT_INFO: {
        grab: { label: 'Grab Food' },
        grab_mart: { label: 'Grab Mart' },
        shopee: { label: 'Shopee Food' },
        shopee_ecom: { label: 'Shopee Ecom' },
        shopee_fresh: { label: 'Shopee Fresh' },
        gojek: { label: 'Gojek' },
        be: { label: 'Be' },
        local: { label: 'Tạo trên <PERSON>' },
        he: { label: '<PERSON><PERSON><PERSON> lý c<PERSON> nhân' },
        lazada: { label: 'La<PERSON>a' },
        tiktok: { label: 'Tik<PERSON>' },
    },
    TRANSACTION_TYPE: {
        payment: { label: 'Thanh toán' },
        adjustment: { label: 'Điều chỉnh' },
        marketing: { label: 'Phí quảng cáo' },
        other: { label: 'Khác' },
    },
    PAY_VIA_WALLET_OR_BANK: [
        ...MOMO_PAYMENT_METHODS,
        'PAYOS',
        'NEXDORPAY',
    ],
    MOMO_PAYMENT_METHODS,
    NTF_BRANDS: ["Nutifood NNDD", "NutiFood NutiMilk", "NutiFood Varna", "NutiFood Bliss", "NutiFood GrowPLUS+"],
    SELF_PICKUP: {
        vendor: 'pick_up',
        code: 'pick_up',
        price: 0,
        name: 'Khách đến cửa hàng lấy hàng (Pick Up)',
        description: 'Khách đến cửa hàng lấy hàng (Pick Up)',
    },
    TAKE_AWAY: {
        vendor: 'take_away',
        code: 'take_away',
        price: 0,
        name: 'Khách mua mang về (Take Away)',
        description: 'Khách mua mang về (Take Away)',
    }
}