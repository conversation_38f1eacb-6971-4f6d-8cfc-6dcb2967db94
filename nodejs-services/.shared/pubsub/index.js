const { PubSub } = require('@google-cloud/pubsub');

const credentials = JSON.parse(process.env.GOOGLE_PRIVATE_KEY);
credentials.private_key = credentials.private_key.split(String.raw`\n`).join('\n'); // Fix bug when encoding

const pubsub = new PubSub({
    projectId: credentials.project_id,
    credentials: credentials
});

const TOPIC_LIST = {
    ORDER: {
        topic: 'PUBSUB_TOPIC_GET_ORDERS',
        subscription: 'PUBSUB_SUB_GET_ORDERS'
    },
    REPORT: {
        topic: 'PUBSUB_TOPIC_GET_REPORTS',
        subscription: 'PUBSUB_SUB_GET_REPORTS'
    },
    BRAND_REQUEST: {
        topic: 'PUBSUB_TOPIC_GET_BRAND_REQUESTS',
        subscription: 'PUBSUB_SUB_GET_BRAND_REQUESTS'
    },
    EXPORT_BRAND_MENU: {
        topic: 'PUBSUB_TOPIC_GET_EXPORT_BRAND_MENU',
        subscription: 'PUBSUB_SUB_GET_EXPORT_BRAND_MENU'
    },
    SITE_MENU: {
        topic: 'PUBSUB_TOPIC_SITE_MENU',
        subscription: 'PUBSUB_SUB_SITE_MENU'
    },
    SITE_PROMOTION: {
        topic: 'PUBSUB_TOPIC_SITE_PROMOTION',
        subscription: 'PUBSUB_SUB_SITE_PROMOTION'
    }
};

const init_topic = async () => {
    for (const key in TOPIC_LIST) {
        const config = TOPIC_LIST[key]
        // config.topic = config.topic + '_DEV'
        // config.subscription = config.subscription + '_DEV'
        try {
            await pubsub.createTopic(config.topic);
            console.log(`Topic ${config.topic} created.`);
            await pubsub.createSubscription(config.topic, config.subscription);
            console.log(`Subscription ${config.subscription} created.`);
        } catch (error) {
            console.error(`Received error while creating topic: ${error.message}`);
        }
    }
}
// init_topic()

async function publisher(topicName, data) {
    const config = TOPIC_LIST[topicName]
    try {
        if (process.env.NODE_ENV !== 'prod') { config.topic = config.topic + '_DEV' }
        const messageId = await pubsub.topic(config.topic).publishMessage({ json: data });
        console.log(`Message ${messageId} published.`);
    } catch (error) {
        console.error(`Received error while publishing: ${error.message}`);
    }
}

async function subscriber(topicName, listen_function) {
    const config = TOPIC_LIST[topicName]
    if (process.env.NODE_ENV !== 'prod') { config.subscription = config.subscription + '_DEV' }
    const subscription = pubsub.subscription(config.subscription, {
        flowControl: {
            maxMessages: 2,
        }
    });
    subscription.on('message', listen_function);
    subscription.on('error', error => {
        console.error('subscription error:', error);
    });
    console.log(`Listening for messages on ${config.subscription}`);
}

module.exports = {
    publisher,
    subscriber
}