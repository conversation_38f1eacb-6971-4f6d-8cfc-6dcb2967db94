package router

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

func HandleTestEvents(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		log.Printf("Error upgrading to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	clients[conn] = &Client{conn, nil, ""}
	defer delete(clients, conn)

	go sendTestEvents(conn)

	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			log.Printf("Error reading WebSocket message: %v", err)
			break
		}
	}
}

func sendTestEvents(conn *websocket.Conn) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mockEvent := generateMockEvent()
			err := conn.WriteMessage(websocket.TextMessage, []byte(mockEvent))
			if err != nil {
				log.Printf("Error sending test event: %v", err)
				return
			}
		}
	}
}

func generateMockEvent() string {
	event := map[string]interface{}{
		"id":        fmt.Sprintf("mock-%d", time.Now().UnixNano()),
		"type":      "new_order",
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   "Bạn có 1 đơn hàng mới từ grab, mã đơn: GF-123",
		"data": map[string]interface{}{
			"brand_ids": []string{"642d61a744b2361680a5837a"},
			"site_ids":  []string{},
			"hub_ids":   []string{},
		},
	}

	eventJSON, _ := json.Marshal(event)
	encodedJSON := strings.ToValidUTF8(string(eventJSON), "")
	return encodedJSON
}
