package router

import (
	"net/http"
	"nexpos/notification-service/t2s"

	"github.com/gin-gonic/gin"
)

func HandleText2Speech(c *gin.Context) {
	text := c.Query("text")
	if text == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "Missing text query parameter",
		})
		return
	}
	audioBuffer, err := t2s.TextToSpeech(text)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.Data(http.StatusOK, "audio/mpeg", audioBuffer)
}
