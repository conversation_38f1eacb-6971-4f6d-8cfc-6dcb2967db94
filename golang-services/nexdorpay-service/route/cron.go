package route

import (
	"net/http"
	"nexpos/nexdorpay-service/sdk/myredis"
	"time"

	"github.com/tidwall/gjson"

	"github.com/gin-gonic/gin"
)

func CronTransactionList(c *gin.Context) {
	redisClient := GetRedis(c)

	lastTransStr := myredis.Get(redisClient, "transactions:latest")
	if lastTransStr != "" {
		if gjson.Parse(lastTransStr).Get("created_at").Time().After(time.Now().Add(-time.Minute * 20)) {
			GetTransactionList(c)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
