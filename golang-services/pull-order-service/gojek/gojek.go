package gojek

import (
	"fmt"

	"crypto/md5"
	"encoding/hex"

	resty "github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

type Token struct {
	Username     string `json:"username"`
	Password     string `json:"password"`
	SiteID       string `json:"site_id"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type Gojek struct {
	Token
}

func NewGojek(username, password, siteID string) *Gojek {
	return &Gojek{
		Token: Token{
			Username: username,
			Password: password,
			SiteID:   siteID,
		},
	}
}

func generateHexString(text string) string {
	hasher := md5.New()
	hasher.Write([]byte(text))
	hash := hex.EncodeToString(hasher.Sum(nil))
	return hash[:16]
}

func (g *Gojek) BaseHeaders() map[string]string {
	headers := map[string]string{
		"x-user-type":        "merchant",
		"x-client-id":        "go-biz-mobile",
		"x-client-secret":    "sPC0qVk7gi76JUoGVfOfcgd7FfuaBv",
		"accept-language":    "vi",
		"x-user-locale":      "vi-VN",
		"x-platform":         "Android",
		"x-appversion":       "4.13.1",
		"x-appid":            "com.gojek.resto",
		"x-deviceos":         "Android 28",
		"x-phonemake":        "samsung",
		"x-phonemodel":       "SM-N950F",
		"accept":             "application/json",
		"gojek-country-code": "VN",
		"gojek-timezone":     "Asia/Ho_Chi_Minh",
		"content-type":       "application/json; charset=UTF-8",
		"user-agent":         "okhttp/3.12.10",
		"x-pushtokentype":    "FCM",
		"x-uniqueid":         generateHexString(g.Username),
	}

	if g.SiteID != "" {
		headers["restaurantuuid"] = g.SiteID
	}
	if g.AccessToken != "" {
		headers["authorization"] = "Bearer " + g.AccessToken
		headers["authentication-type"] = "go-id"
	}

	return headers
}

func (g *Gojek) GetToken() error {
	if g.RefreshToken == "" {
		return nil
	}

	url := "https://goid.gojekapi.com/goid/token"
	headers := g.BaseHeaders()
	payload := map[string]interface{}{
		"grant_type":    "refresh_token",
		"client_id":     "go-biz-mobile",
		"client_secret": "sPC0qVk7gi76JUoGVfOfcgd7FfuaBv",
		"data": map[string]string{
			"refresh_token": g.RefreshToken,
		},
	}

	client := resty.New()
	resp, err := client.R().
		SetHeaders(headers).
		SetBody(payload).
		Post(url)
	if err != nil {
		return err
	}

	respJ := gjson.ParseBytes(resp.Body())
	fmt.Println(respJ.Raw)
	return nil
}

func (g *Gojek) GetOrderList(status string) ([]gjson.Result, error) {
	if g.AccessToken == "" {
		return nil, nil
	}

	mapStatus := map[string]string{
		"PENDING": "ONGOING",
		"DOING":   "ONGOING",
		"FINISH":  "COMPLETED",
		"CANCEL":  "UNFULFILLED",
	}[status]

	if mapStatus == "" {
		return nil, nil
	}

	var orders []gjson.Result
	url := fmt.Sprintf("https://api.gojekapi.com/mocha/v3/orders?restaurant_id=%s&status=%s&limit=50&page=1", g.SiteID, mapStatus)
	headers := g.BaseHeaders()

	client := resty.New()
	resp, err := client.R().
		SetHeaders(headers).
		Get(url)
	if err != nil {
		return nil, err
	}

	respJ := gjson.ParseBytes(resp.Body())
	if respJ.Get("orders").Raw == "" {
		return nil, nil
	}

	respJ.Get("orders").ForEach(func(key, value gjson.Result) bool {
		orders = append(orders, value)
		return true
	})

	return orders, nil
}
