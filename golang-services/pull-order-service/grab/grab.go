package grab

import (
	"fmt"
	"time"

	"crypto/md5"
	"encoding/hex"

	resty "github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
)

type Token struct {
	Username     string `json:"username"`
	Password     string `json:"password"`
	SiteID       string `json:"site_id"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type Grab struct {
	Token
}

func NewGojek(username, password, siteID string) *Grab {
	return &Grab{
		Token: Token{
			Username: username,
			Password: password,
			SiteID:   siteID,
		},
	}
}

func generateHexString(text string) string {
	hasher := md5.New()
	hasher.Write([]byte(text))
	hash := hex.EncodeToString(hasher.Sum(nil))
	return hash[:16]
}

func (g *Grab) BaseHeaders(isMart bool) map[string]string {
	headers := map[string]string{
		"user-profile-data":       "",
		"device-os":               "Android",
		"user-agent":              "Grab Merchant/4.87.1 (android 9; Build 164)",
		"business-type":           "FOOD",
		"x-agent":                 "mexapp",
		"authorization":           g.AccessToken,
		"mex-country":             "VN",
		"mex-type":                "MEXUSERS",
		"x-platform-type":         "android",
		"accept-language":         "vi",
		"networkkit-disable-gzip": "true",
		"device-model":            "SM-N960N",
	}

	if isMart {
		headers["business-type"] = "SHOP"
	}
	return headers
}

func (g *Grab) GetToken() error {
	return nil
}

func (g *Grab) GetOrderList(status string) ([]gjson.Result, error) {
	if g.AccessToken == "" {
		return nil, nil
	}

	if status == "PENDING" || status == "PRE_ORDER" {
		return nil, nil
	}

	var orders []gjson.Result
	if status == "DOING" {
		url := fmt.Sprintf("https://api.grab.com/food/merchant/v3/orders-pagination?pageType=Preparing&autoAcceptGroup=1&timestamp=%d", time.Now().Unix())
		headers := g.BaseHeaders(false)
		client := resty.New()
		resp, err := client.R().
			SetHeaders(headers).
			Get(url)
		if err != nil {
			return nil, err
		}

		respJ := gjson.ParseBytes(resp.Body())
		if respJ.Get("orders").Raw == "" {
			return nil, nil
		}

		respJ.Get("orders").ForEach(func(key, value gjson.Result) bool {
			orders = append(orders, value)
			return true
		})
	}

	if status == "FINISH" || status == "CANCEL" {
		startTime, endTime := getUTCStartAndEndDateFromNow()
		url := fmt.Sprintf("https://api.grab.com/food/merchant/v1/reports/daily-pagination?pageIndex=0&pageSize=100&startTime=%s&endTime=%s", startTime, endTime)
		headers := g.BaseHeaders(false)

		client := resty.New()
		resp, err := client.R().
			SetHeaders(headers).
			Get(url)
		if err != nil {
			fmt.Println(err)
		}

		respJ := gjson.ParseBytes(resp.Body())
		if status == "CANCEL" {
			respJ.Get("statements").ForEach(func(key, value gjson.Result) bool {
				if contains([]string{"FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"}, value.Get("deliveryStatus").String()) {
					orders = append(orders, value)
				}
				return true
			})
		} else {
			respJ.Get("statements").ForEach(func(key, value gjson.Result) bool {
				if !contains([]string{"FAILED", "CANCELLED", "CANCELLED_PASSENGER", "CANCELLED_OPERATOR", "CANCELLED_MAX"}, value.Get("deliveryStatus").String()) {
					orders = append(orders, value)
				}
				return true
			})
		}
	}

	return orders, nil
}

func contains(arr []string, str string) bool {
	for _, a := range arr {
		if a == str {
			return true
		}
	}
	return false
}

func getUTCStartAndEndDateFromNow() (string, string) {
	startTime := time.Now().UTC().Add(-24 * time.Hour).Format("2006-01-02T15:04:05.000Z")
	endTime := time.Now().UTC().Format("2006-01-02T15:04:05.000Z")
	return startTime, endTime
}
