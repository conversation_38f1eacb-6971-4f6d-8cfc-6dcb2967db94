package sdk

import (
	"fmt"
	"testing"
)

func Test_decodeAES(t *testing.T) {
	text := `IHdGsbYoTDp/5HSnjo+Ebfg2732GfRZRdwSUrJMlKHil3xdjxgUf8xarI92YEqbXf0qOTHW7iR1J4SUvA3hV871EgVZ0F9fcpTJ7F7YbTDq0PeD6gNIxpGCMyteqGOPjVJurJJ/Cgr8wdy9md6TzoPGOFlY4FqsPhC0BZp+y+HOSorRWXloQYMVRRM8G00peAbdHmkHPuZ5iUp95nLO/KNdwzxWXTKoGQffshmdPy0lCKeEYBecgJYXm4w34o6c6L1xpWfLZrrCOqWolpd46qw==`
	fmt.Println(decodeAES(text, secret))
}
