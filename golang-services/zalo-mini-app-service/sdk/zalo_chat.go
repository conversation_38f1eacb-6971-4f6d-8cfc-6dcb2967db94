package sdk

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

const (
	// Leo
	// secret = "FNNZkV4v7viToCRJVAEvuA=="
	// cookie = "__zi=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8qyc-oZaKzHZdcPuglOILQ4T9xgh3Wt.1; __zi-legacy=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8qyc-oZaKzHZdcPuglOILQ4T9xgh3Wt.1; _ga_NVN38N77J3=GS1.2.1709695510.6.1.1709697811.0.0.0; _ga_E63JS7SPBL=GS1.1.1709695508.7.1.1709698092.53.0.0; zpsid=NhBE.122129381.53.88lkndOOArAJasnLUXWhKWDhMMD4AXfWJoiJOrnekf_OLbSRTzJSjHCOAr8; zpw_sek=uftM.122129381.a0.gWLgy1FvJg_sSWceClbEhsZRAyGnmspfNRSKn4NDE9Xba1h0KAi4-LAyEDfIn7EDRjC-FjvN0DDQ-81Oz9nEhm; _ga_QKW3RCKZFH=GS1.1.1710500208.27.0.1710500209.0.0.0; app.event.zalo.me=6442221467707282414; zoaw_sek=Uq-y.1089481059.1.6irWOCb0b0ggQMHAoK2K2ib0b0h9G9GAo5jVzAn0b0e; zoaw_type=0; _zlang=vn; _gid=GA1.2.906429564.1711807882; _ga=GA1.1.1013720071.1702304665; _ga_907M127EPP=GS1.1.1711809716.23.0.1711810066.60.0.0"

	// NexDor
	secret          = "YBXnJ7jNAMGFeM5lImVEJA=="
	cookie          = "_zlang=vn; _ga=GA1.2.944687462.1724393785; _gid=GA1.2.1398434937.1724393785; zpsid=fHXx.405087717.3.l8_4iy0jdMSb2BX_p2sTPxLUxrFm6ArVzngiKBivuyhaXeELmOcthwOjdMS; zpw_sek=J1w3.405087717.a0.DVB7kElRjeqE_fELojksZP3vq-R9uPJKgOFOnzon-CocdF3efkxQn8QDyyUgveklbg2YyqR_7PCPsXEd8xssZG; __zi=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8W-dEsidKnPXtMQwwdLJbU6Tf7XhZKr.1; __zi-legacy=3000.SSZzejyD6zOgdh2mtnLQWYQN_RAG01ICFjIXe9fEM8W-dEsidKnPXtMQwwdLJbU6Tf7XhZKr.1; ozi=2000.QOBlzDCV2uGerkFzm09LsMJMuVN42L7TBD_ZyumBNzmlrEdqDJK.1; app.event.zalo.me=6425442288552697762"
	imei            = "9c586ec2-d5ea-4bda-b18c-a70422b6fcac-3d96f8e03a42123e5523adf5c57607ad"
	baseURL         = "https://tt-chat2-wpa.chat.zalo.me"
	groupBaseURL    = "https://tt-group-wpa.chat.zalo.me"
	filesBaseURL    = "https://tt-files-wpa.chat.zalo.me"
	userAgent       = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
	zpwVer, zpwType = "637", "30"
)

func encodeAES(message, secret string) string {
	key, _ := base64.StdEncoding.DecodeString(secret)
	iv, _ := hex.DecodeString("00000000000000000000000000000000")

	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	padded := pkcs7Pad([]byte(message), block.BlockSize())
	ciphertext := make([]byte, len(padded))
	mode.CryptBlocks(ciphertext, padded)

	return base64.StdEncoding.EncodeToString(ciphertext)
}

func decodeAES(ciphertext, secret string) string {
	key, _ := base64.StdEncoding.DecodeString(secret)
	iv, _ := hex.DecodeString("00000000000000000000000000000000")

	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	data, _ := base64.StdEncoding.DecodeString(ciphertext)
	plaintext := make([]byte, len(data))
	mode.CryptBlocks(plaintext, data)

	return string(pkcs7Unpad(plaintext, block.BlockSize()))
}

func pkcs7Pad(data []byte, blockSize int) []byte {
	padLen := blockSize - (len(data) % blockSize)
	pad := bytes.Repeat([]byte{byte(padLen)}, padLen)
	return append(data, pad...)
}

func pkcs7Unpad(data []byte, blockSize int) []byte {
	padLen := int(data[len(data)-1])
	return data[:len(data)-padLen]
}

func getParamsEncrypted(message interface{}) string {
	jsonData, _ := json.Marshal(message)
	return encodeAES(string(jsonData), secret)
}

type Style struct {
	Start int    `json:"start"`
	Len   int    `json:"len"`
	St    string `json:"st"`
}

type TextProperties struct {
	Styles []Style `json:"styles"`
	Ver    int     `json:"ver"`
}

// parseHTMLToZaloTextProperties parses an HTML string into a Zalo text string
// Example: Thường <b>In Đạm</b> <i>In Nghiêng</i> <span style="color:#db342e">Màu Đỏ</span>
// [<bc style="color:#db342e">Xin Chào Mọi Người</bc>]
func parseHTMLToZaloTextProperties(input string) (string, string, error) {
	text := ""
	currentIndex := 0
	var styles []Style

	// Define regex patterns for opening and closing tags
	openTagPattern := regexp.MustCompile(`<(\w+)([^>]*)>`)
	closeTagPattern := regexp.MustCompile(`</(\w+)>`)

	// Iterate over the input string
	cursor := 0
	for {
		openTagLoc := openTagPattern.FindStringSubmatchIndex(input[cursor:])
		if openTagLoc == nil {
			text += input[cursor:]
			break
		}

		// Add text up to the next tag to the output
		text += input[cursor : cursor+openTagLoc[0]]
		currentIndex += utf8.RuneCountInString(input[cursor : cursor+openTagLoc[0]])

		tag := input[cursor+openTagLoc[2] : cursor+openTagLoc[3]]
		attributes := input[cursor+openTagLoc[4] : cursor+openTagLoc[5]]
		styleStart := currentIndex

		// Move cursor past the current tag
		cursor += openTagLoc[1]

		// Find the corresponding closing tag
		// closeTag := fmt.Sprintf("</%s>", tag)
		closeTagLoc := closeTagPattern.FindStringIndex(input[cursor:])
		if closeTagLoc == nil {
			return "", "", fmt.Errorf("unmatched tag: %s", tag)
		}

		// Calculate style length
		styleLength := utf8.RuneCountInString(input[cursor : cursor+closeTagLoc[0]])

		// Update the text and current index
		text += input[cursor : cursor+closeTagLoc[0]]
		currentIndex += styleLength

		// Move cursor past the closing tag
		cursor += closeTagLoc[1]

		// Determine the style
		style := ""
		switch tag {
		case "b":
			style = "b"
		case "i":
			style = "i"
		case "span":
			colorMatch := regexp.MustCompile(`color:#([0-9a-fA-F]+)`).FindStringSubmatch(attributes)
			if colorMatch != nil {
				style = "c_" + colorMatch[1]
			}
		case "bc":
			colorMatch := regexp.MustCompile(`color:#([0-9a-fA-F]+)`).FindStringSubmatch(attributes)
			if colorMatch != nil {
				style = "b,c_" + colorMatch[1]
			}
		}

		if style != "" {
			styles = append(styles, Style{
				Start: styleStart,
				Len:   styleLength,
				St:    style,
			})
		}
	}

	textProperties := TextProperties{
		Styles: styles,
		Ver:    0,
	}
	propertiesJSON, err := json.Marshal(textProperties)
	if err != nil {
		return "", "", err
	}

	return text, string(propertiesJSON), nil
}

func getHeaders() map[string]string {
	return map[string]string{
		"Accept":          "application/json, text/plain, */*",
		"Accept-Language": "en-US,en;q=0.9",
		"Content-Type":    "application/x-www-form-urlencoded",
		"Cookie":          cookie,
		"Origin":          "https://chat.zalo.me",
		"Referer":         "https://chat.zalo.me/",
		"User-Agent":      userAgent,
	}
}

func getFileHeaders(w *multipart.Writer) map[string]string {
	return map[string]string{
		"Accept":          "application/json, text/plain, */*",
		"Accept-Language": "en-US,en;q=0.9",
		"Content-Type":    w.FormDataContentType(),
		"Cookie":          cookie,
		"Origin":          "https://chat.zalo.me",
		"Referer":         "https://chat.zalo.me/",
		"User-Agent":      userAgent,
	}
}

func SendMessageToUser(toid int64, message string) {
	params := map[string]interface{}{
		"message":  message,
		"clientId": strconv.FormatInt(time.Now().UnixNano(), 10),
		"imei":     imei,
		"ttl":      0,
		"toid":     strconv.FormatInt(toid, 10),
	}

	data := map[string]string{
		"params": getParamsEncrypted(params),
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(data).
		Post(baseURL + "/api/message/sms?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println(resp.String())
}
func GetGroupInfo(shareLink string) map[string]interface{} {
	groupName := strings.TrimPrefix(shareLink, "https://zalo.me/g/")
	params := map[string]interface{}{
		"link":               "https://zalo.me/g/" + groupName,
		"avatar_size":        120,
		"member_avatar_size": 120,
		"mpage":              1,
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"params":   getParamsEncrypted(params),
		}).
		Get(groupBaseURL + "/api/group/link/ginfo")

	if err != nil {
		fmt.Println("Error:", err)
		return nil
	}
	fmt.Println(resp.String())
	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := decodeAES(respDataEncrypted, secret)
	return cast.ToStringMap(gjson.Parse(respData).Get("data").Value())
}

func SendMessageToGroup(shareLink, message, urgency string) {
	groupInfo := GetGroupInfo(shareLink)
	textMessage, textProperties, err := parseHTMLToZaloTextProperties(message)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	params := map[string]interface{}{
		"message":    textMessage,
		"clientId":   strconv.FormatInt(time.Now().UnixNano(), 10),
		"imei":       imei,
		"ttl":        0,
		"visibility": 0,
		"grid":       groupInfo["groupId"],
	}
	if urgency != "" {
		params["metaData"] = map[string]interface{}{
			"urgency": cast.ToInt(urgency),
		}
	}

	if textProperties != "" {
		params["textProperties"] = textProperties
	}

	data := map[string]string{
		"params": getParamsEncrypted(params),
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(data).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
		}).
		Post(groupBaseURL + "/api/group/sendmsg")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := decodeAES(respDataEncrypted, secret)
	fmt.Println(cast.ToStringMap(gjson.Parse(respData).String()))
}

func SendImageToGroup(shareLink string, imageBuff []byte) {
	groupInfo := GetGroupInfo(shareLink)

	bodyBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(bodyBuff)
	part, _ := writer.CreateFormFile("chunkContent", "image.jpg")
	part.Write(imageBuff)

	params := map[string]interface{}{
		"totalChunk": 1,
		"fileName":   "image.jpg",
		"clientId":   strconv.FormatInt(time.Now().UnixNano(), 10),
		"totalSize":  len(imageBuff),
		"imei":       imei,
		"grid":       groupInfo["groupId"],
		"isE2EE":     0,
		"chunkId":    1,
	}

	writer.Close()

	resp, err := resty.New().R().
		SetHeaders(getFileHeaders(writer)).
		SetBody(bodyBuff.Bytes()).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "11",
			"params":   getParamsEncrypted(params),
		}).
		Post(filesBaseURL + "/api/group/photo_original/upload")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	respDataEncrypted := gjson.Get(resp.String(), "data").String()
	respData := gjson.Parse(decodeAES(respDataEncrypted, secret)).Get("data")
	fmt.Println(respData.String())
	params2 := map[string]interface{}{
		"photoId":  respData.Get("clientFileId").Value(),
		"clientId": strconv.FormatInt(time.Now().UnixNano(), 10),
		"desc":     "",
		"grid":     groupInfo["groupId"],
		"rawUrl":   respData.Get("hdUrl").Value(),
		"thumbUrl": respData.Get("thumbUrl").Value(),
		"oriUrl":   respData.Get("normalUrl").Value(),
		"hdUrl":    respData.Get("hdUrl").Value(),
		"zsource":  -1,
		"jcp":      "{\"sendSource\":1}",
		"ttl":      0,
		"imei":     imei,
	}

	resp2, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(params2),
		}).
		Post(filesBaseURL + "/api/group/photo_original/send?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	resp2DataEncrypted := gjson.Get(resp2.String(), "data").String()
	resp2Data := gjson.Parse(decodeAES(resp2DataEncrypted, secret)).Get("data")
	fmt.Println(resp2Data.String())
}

func SendImageToUser(userID int64, imageBuff []byte) {
	bodyBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(bodyBuff)
	part, _ := writer.CreateFormFile("chunkContent", "image.jpg")
	part.Write(imageBuff)
	writer.Close()

	params := map[string]interface{}{
		"totalChunk": 1,
		"fileName":   "image.jpg",
		"clientId":   strconv.FormatInt(time.Now().UnixNano(), 10),
		"totalSize":  len(imageBuff),
		"imei":       imei,
		"toid":       strconv.FormatInt(userID, 10),
		"isE2EE":     0,
		"chunkId":    1,
	}

	resp, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetBody(bodyBuff.Bytes()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(params),
		}).
		SetQueryParams(map[string]string{
			"zpw_ver":  zpwVer,
			"zpw_type": zpwType,
			"type":     "2",
		}).
		Post(filesBaseURL + "/api/message/photo_original/upload")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	respData := gjson.Get(resp.String(), "data")

	params2 := map[string]interface{}{
		"clientId":  strconv.FormatInt(time.Now().UnixNano(), 10),
		"desc":      "",
		"hdUrl":     respData.Get("hdUrl").Value(),
		"imei":      imei,
		"jcp":       "{\"sendSource\":1}",
		"normalUrl": respData.Get("normalUrl").Value(),
		"photoId":   respData.Get("photoId").Value(),
		"rawUrl":    respData.Get("normalUrl").Value(),
		"thumbSize": "7068",
		"thumbUrl":  respData.Get("thumbUrl").Value(),
		"toid":      strconv.FormatInt(userID, 10),
		"ttl":       0,
		"zsource":   -1,
	}

	resp2, err := resty.New().R().
		SetHeaders(getHeaders()).
		SetFormData(map[string]string{
			"params": getParamsEncrypted(params2),
		}).
		Post(filesBaseURL + "/api/message/photo_original/send?zpw_ver=" + zpwVer + "&zpw_type=" + zpwType + "&nretry=0")

	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	fmt.Println(resp2.String())
}
