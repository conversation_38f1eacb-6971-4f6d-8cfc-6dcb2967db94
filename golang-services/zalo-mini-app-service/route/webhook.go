package route

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// type ZaloWebhookRequest struct {
// 	AccessToken string `json:"access_token"`
// 	Code        string `json:"code"`
// }

func ZaloWebHook(c *gin.Context) {
	var requestBody map[string]interface{}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
		})
	}
	println(requestBody)

	// Process the raw JSON or perform any other operations

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
