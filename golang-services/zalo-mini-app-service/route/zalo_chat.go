package route

import (
	"io/ioutil"
	"net/http"
	"nexpos/zalo-mini-app-service/sdk"

	"github.com/gin-gonic/gin"
)

func SendMessageToGroup(c *gin.Context) {
	shareLink := c.PostForm("shareLink")
	message := c.PostForm("message")
	urgency := c.PostForm("urgency")
	sdk.SendMessageToGroup(shareLink, message, urgency)
	c.JSON(http.StatusOK, gin.H{"message": "Message sent to group"})
}

func SendImageToGroup(c *gin.Context) {
	shareLink := c.PostForm("shareLink")
	file, err := c.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Open the uploaded file
	fileReader, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	defer fileReader.Close()

	// Read the image file
	imageBuff, err := ioutil.ReadAll(fileReader)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	sdk.SendImageToGroup(shareLink, imageBuff)
	c.JSON(http.StatusOK, gin.H{"message": "Image sent to group"})
}
