package redis

import (
	"context"
	"encoding/json"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/thoas/go-funk"
)

var redisClient *redis.Client

func InitRedis(addr string) {
	redisClient = redis.NewClient(&redis.Options{
		Addr: addr,
	})
}

func GetObj(key string) (map[string]interface{}, error) {
	ctx := context.Background()
	val, err := redisClient.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, err
	}

	return result, nil
}

func SetObj(key string, value interface{}, expiration time.Duration) error {
	ctx := context.Background()
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return redisClient.Set(ctx, key, jsonData, expiration).Err()
}

// Queue operations client
type QueueClient struct{}

var Queue = &QueueClient{}

// GetQueue retrieves items from a queue
func (q *QueueClient) GetQueue(name string) ([]map[string]interface{}, error) {
	ctx := context.Background()
	val, err := redisClient.Get(ctx, name).Result()
	if err != nil {
		if err == redis.Nil {
			return []map[string]interface{}{}, nil
		}
		return nil, err
	}

	var result []map[string]interface{}
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, err
	}

	return result, nil
}

// SetQueue stores items in a queue with updated timestamp
func (q *QueueClient) SetQueue(name string, queueList []map[string]interface{}) error {
	ctx := context.Background()

	// Add timestamp to each item
	now := time.Now().Unix()
	updatedList := funk.Map(queueList, func(item map[string]interface{}) map[string]interface{} {
		newItem := make(map[string]interface{})
		for k, v := range item {
			newItem[k] = v
		}
		newItem["updated_at_unix"] = now
		return newItem
	}).([]map[string]interface{})

	jsonData, err := json.Marshal(updatedList)
	if err != nil {
		return err
	}

	return redisClient.Set(ctx, name, jsonData, 24*time.Hour).Err()
}

// LSwapQueueByID finds an item by ID and moves it to the front of the queue
func (q *QueueClient) LSwapQueueByID(name, id string) ([]map[string]interface{}, error) {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return nil, err
	}

	// Find item by ID
	itemIndex := -1
	for i, item := range queueList {
		if itemID, ok := item["_id"].(string); ok && itemID == id {
			itemIndex = i
			break
		}
	}

	if itemIndex == -1 {
		return queueList, nil
	}

	// Remove item and add to front
	item := queueList[itemIndex]
	queueList = append(queueList[:itemIndex], queueList[itemIndex+1:]...)
	queueList = append([]map[string]interface{}{item}, queueList...)

	// Save updated queue
	if err := q.SetQueue(name, queueList); err != nil {
		return nil, err
	}

	return queueList, nil
}

// FindAndSetQueueCount increments count for an existing item or adds a new one
func (q *QueueClient) FindAndSetQueueCount(name string, query func(map[string]interface{}) bool, item map[string]interface{}) error {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return err
	}

	// Find matching item
	var foundItem map[string]interface{}
	for _, qItem := range queueList {
		if query(qItem) {
			foundItem = qItem
			break
		}
	}

	now := time.Now().Unix()
	item["updated_at_unix"] = now

	if foundItem != nil {
		// Update existing item
		count, _ := foundItem["count"].(float64)
		foundItem["count"] = count + 1

		// Copy all properties from item to foundItem
		for k, v := range item {
			foundItem[k] = v
		}

		return q.SetQueue(name, queueList)
	} else {
		// Add new item
		item["count"] = 1
		queueList = append(queueList, item)
		return q.SetQueue(name, queueList)
	}
}

// PickQueue selects items from the queue for processing
func (q *QueueClient) PickQueue(name string, options map[string]interface{}) ([]map[string]interface{}, error) {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return nil, err
	}

	if len(queueList) == 0 {
		return []map[string]interface{}{}, nil
	}

	// Extract options with defaults
	size := 5
	if val, ok := options["size"].(int); ok {
		size = val
	}

	minDuration := 60 // default 60 seconds
	if val, ok := options["min_duration"].(int); ok {
		minDuration = val
	}

	deleteQueue := false
	if val, ok := options["delete_queue"].(bool); ok {
		deleteQueue = val
	}

	// Calculate number of items to pick
	pickCount := size
	if pickCount > len(queueList) {
		pickCount = len(queueList)
	}

	// Select items from the front of the queue
	selectedItems := queueList[:pickCount]
	remainingItems := queueList[pickCount:]

	// Check if enough time has passed since the last update
	if firstItem := selectedItems[0]; firstItem != nil {
		if updatedAt, ok := firstItem["updated_at_unix"].(float64); ok {
			now := float64(time.Now().Unix())
			if now-updatedAt < float64(minDuration) {
				return []map[string]interface{}{}, nil
			}
		}
	}

	if !deleteQueue {
		// Update timestamps and move to end of queue
		now := time.Now().Unix()
		for _, item := range selectedItems {
			item["updated_at_unix"] = now
			remainingItems = append(remainingItems, item)
		}
	}

	// Save the updated queue
	if err := redisClient.Set(context.Background(), name, remainingItems, 24*time.Hour).Err(); err != nil {
		return nil, err
	}

	return selectedItems, nil
}

// DeleteQueue removes specified items from the queue
func (q *QueueClient) DeleteQueue(name string, items []map[string]interface{}) error {
	queueList, err := q.GetQueue(name)
	if err != nil {
		return err
	}

	// Create a map of IDs to remove for quick lookup
	removeIDs := make(map[string]bool)
	for _, item := range items {
		if id, ok := item["_id"].(string); ok {
			removeIDs[id] = true
		}
	}

	// Filter out items to be removed
	var filteredList []map[string]interface{}
	for _, item := range queueList {
		if id, ok := item["_id"].(string); ok && !removeIDs[id] {
			filteredList = append(filteredList, item)
		}
	}

	// Save the filtered queue
	return q.SetQueue(name, filteredList)
}
