package router

import (
	"nexpos/merchant-service/router/handlers"
	"nexpos/sdk/middlewares"

	"github.com/gin-gonic/gin"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	required := middlewares.RequireAuth

	r.GET("health", handlers.HealthCheck)
	api := r.Group("api")
	{
		// Cron jobs
		cronJobs := api.Group("cron")
		{
			cronJobs.GET("/test", required("internal"), handlers.CronSiteOrders)
			cronJobs.GET("/site-orders", handlers.CronSiteOrders)
		}

		// Add other routes here as needed
	}

	return r
}
