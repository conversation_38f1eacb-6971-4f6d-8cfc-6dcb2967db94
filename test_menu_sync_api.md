# Menu Sync API Test Documentation

## Updated /menu/sync API

The `/menu/sync` API has been updated to support selective item synchronization with the following new features:

### New Request Format

```json
{
  "sync_items": ["item_id_1", "item_id_2", "item_id_3"],
  "delete_items": ["item_id_4", "item_id_5"]
}
```

### API Endpoint
```
POST /v1/brand-service/sites/{site_id}/menu/sync
```

### Request Body (Optional)
- `sync_items` (array of strings): List of item IDs to sync. If empty or not provided, all items will be synced (backward compatibility)
- `delete_items` (array of strings): List of item IDs to delete (reserved for future implementation)

### Response Format
```json
{
  "success": true,
  "sync_request_id": "507f1f77bcf86cd799439011",
  "message": "Menu synchronization started"
}
```

## New Features Implemented

### 1. MenuSyncLog Model
- Detailed logging for each platform sync operation (batch-level logging)
- Tracks platform, status, errors, sync_items, delete_items, and completion time
- Stored in `menu_sync_logs` table

### 2. Selective Item Sync
- Only sync specified items instead of all menu items
- Backward compatible - if no items specified, syncs all items
- Supports all platforms: Grab, Grab Mart, Shopee, BE

### 3. Enhanced BE Menu Sync
- Implemented actual BE menu synchronization
- Uses BE API to update menu item status
- Matches items by name between site menu and BE menu
- Updates item active/inactive status

### 4. Improved Logging
- Platform-level sync results (batch logging)
- Error tracking per platform
- Completion time tracking
- Detailed request/response data

## Database Changes

### MenuSyncRequest Table Updates
```sql
ALTER TABLE menu_sync_requests 
ADD COLUMN sync_items JSONB,
ADD COLUMN delete_items JSONB;
```

### New MenuSyncLog Table
```sql
CREATE TABLE menu_sync_logs (
    id VARCHAR PRIMARY KEY DEFAULT gen_object_id(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    sync_request_id VARCHAR,
    site_id VARCHAR,
    platform VARCHAR,
    status VARCHAR CHECK (status IN ('success', 'failed')),
    error_msg TEXT,
    request_data JSONB,
    response_data JSONB,
    sync_items JSONB,
    delete_items JSONB,
    completed_time TIMESTAMP
);
```

## Testing Examples

### 1. Sync All Items (Backward Compatible)
```bash
curl -X POST "http://localhost:8080/v1/brand-service/sites/SITE_ID/menu/sync" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Sync Specific Items
```bash
curl -X POST "http://localhost:8080/v1/brand-service/sites/SITE_ID/menu/sync" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "sync_items": ["item_123", "item_456", "item_789"]
  }'
```

### 3. Check Sync Status
```bash
curl -X GET "http://localhost:8080/v1/brand-service/sites/SITE_ID/menu/sync?status=processing" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Platform Support

### Grab & Grab Mart
- ✅ Selective sync implemented
- ✅ Detailed logging
- ✅ Error handling

### Shopee
- ✅ Selective sync implemented  
- ✅ Detailed logging
- ✅ Error handling

### BE (Baemin)
- ✅ **NEW**: Full sync implementation
- ✅ Menu item status updates
- ✅ Name-based item matching
- ✅ Detailed logging

## Migration Required

Run the migration service to create the new MenuSyncLog table:
```bash
cd golang-services/migration-service
go run main.go
```
